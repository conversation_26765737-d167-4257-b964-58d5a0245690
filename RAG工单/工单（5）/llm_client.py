import openai
import logging

# 全局LLM客户端
llm_client = None


def init_llm_client():
    global llm_client
    try:
        llm_client = openai.OpenAI(
            api_key="ms-6f7de516-7098-44af-969d-b1d28d65ec00",
            base_url="https://api-inference.modelscope.cn/v1/",
            timeout=30
        )
        return llm_client
    except Exception as e:
        logging.error(f"LLM client initialization error: {str(e)}")
        raise


def generate_answer(query, context, client, chat_history=[]):
    try:
        # 构建系统消息
        messages = [
            {
                "role": "system",
                "content": """你是一个智能文档问答助手，具有以下特点：
1. 基于上传的文档内容准确回答用户问题
2. 能够理解多轮对话的上下文关系
3. 使用Markdown格式组织回答内容
4. 当信息不足时会诚实说明
5. 回答简洁明了，重点突出

请根据文档内容和对话历史，为用户提供准确、有用的回答。"""
            }
        ]

        # 处理历史对话，只保留最近的几轮对话以避免上下文过长
        if chat_history:
            # 只保留最近的6轮对话（12条消息）
            recent_history = chat_history[-12:] if len(chat_history) > 12 else chat_history
            # 过滤掉timestamp字段，只保留role和content
            filtered_history = []
            for msg in recent_history:
                if isinstance(msg, dict) and 'role' in msg and 'content' in msg:
                    filtered_history.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
            messages.extend(filtered_history)

        # 添加上下文信息
        context_text = "\n\n".join([item["text"] for item in context])
        if len(context_text) > 4000:
            context_text = context_text[:4000] + "...[内容已截断]"

        if context_text.strip():
            messages.append({
                "role": "system",
                "content": f"相关文档内容：\n{context_text}\n\n请基于以上文档内容回答用户问题。"
            })

        # 添加当前查询
        messages.append({
            "role": "user",
            "content": query
        })

        response = client.chat.completions.create(
            model="Qwen/Qwen2.5-Coder-32B-Instruct",
            messages=messages,
            stream=False,
            timeout=30,
            max_tokens=2000,
            temperature=0.7
        )

        return response.choices[0].message.content.strip()

    except openai.RateLimitError:
        logging.error("API请求达到速率限制")
        return "抱歉，当前请求过于频繁，请稍后再试。"
    except openai.Timeout:
        logging.error("API请求超时")
        return "请求处理超时，请稍后再试。"
    except Exception as e:
        logging.error(f"生成答案错误: {str(e)}")
        return "生成答案时出现错误，请稍后再试。"