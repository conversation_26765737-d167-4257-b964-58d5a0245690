新增功能模块
1. 独立的语音聊天模块 (voice_chat.py)
✅ 完整对话流程: 语音输入 → 文本识别 → 大模型对话 → 语音合成 → 语音输出
✅ 多格式支持: WAV、MP3、M4A、FLAC、AAC、OGG
✅ 智能处理: 自动音频转换、降噪、格式优化
✅ 对话记忆: 保持多轮对话的上下文记忆
✅ 语音合成: 使用pyttsx3进行本地语音合成
2. 后端API接口
✅ /voice-chat: 完整的语音对话处理接口
✅ /voice-chat-history: 获取语音聊天历史
✅ /clear-voice-chat: 清除语音聊天历史
✅ /download-voice/<filename>: 下载AI语音回复文件
3. 前端界面优化
✅ 语音聊天区域: 独立的语音对话界面
✅ 对话历史显示: 完整的语音对话记录展示
✅ 语音播放控制: 一键播放AI语音回复
✅ 文本显示切换: 可选择显示/隐藏对话文本
✅ 拖拽上传: 支持拖拽音频文件上传