# -*- coding: utf-8 -*-
"""
配置文件
集中管理所有系统配置参数，提高可维护性和可扩展性
"""

import os
from typing import Dict, List, Optional

class Config:
    """
    系统配置类
    包含所有模块的配置参数
    """
    
    # ==================== 基础配置 ====================
    
    # 应用基础配置
    APP_NAME = "智能文档问答系统"
    APP_VERSION = "2.0.0"
    DEBUG = True
    SECRET_KEY = os.environ.get('SECRET_KEY', 'your-secret-key-here')
    
    # 服务器配置
    HOST = '0.0.0.0'
    PORT = 5000
    
    # 文件上传配置
    UPLOAD_FOLDER = 'uploads'
    MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50MB
    ALLOWED_EXTENSIONS = {
        'txt', 'pdf', 'docx', 'doc',           # 文档类型
        'jpg', 'jpeg', 'png', 'bmp', 'tiff',   # 图片类型
        'xlsx', 'xls', 'csv',                  # 表格类型
        'wav', 'mp3', 'm4a', 'flac', 'aac'     # 音频类型
    }
    
    # ==================== 数据库配置 ====================
    
    # Milvus向量数据库配置
    MILVUS_HOST = 'localhost'
    MILVUS_PORT = '19530'
    MILVUS_COLLECTION_NAME = 'document_vectors'
    MILVUS_VECTOR_DIM = 1024  # BGE-M3模型输出维度
    
    # Milvus索引配置
    MILVUS_INDEX_TYPE = 'IVF_SQ8'  # 索引类型：IVF_FLAT, IVF_SQ8, HNSW
    MILVUS_METRIC_TYPE = 'L2'      # 距离度量：L2, IP, COSINE
    MILVUS_NLIST = 2048            # IVF索引的聚类中心数量
    MILVUS_NPROBE = 16             # 搜索时的探测聚类数量
    
    # Redis配置
    REDIS_HOST = 'localhost'
    REDIS_PORT = 6379
    REDIS_DB = 0
    REDIS_PASSWORD = None
    SESSION_EXPIRE = 1800  # 会话过期时间（秒）
    
    # ==================== 模型配置 ====================
    
    # 嵌入模型配置
    EMBEDDING_MODEL_PATH = r"D:\model\BAAI\bge-m3"
    EMBEDDING_MODEL_NAME = "BGE-M3"
    EMBEDDING_BATCH_SIZE = 32
    EMBEDDING_MAX_LENGTH = 512
    
    # 重排序模型配置
    RERANKER_MODEL_PATH = r"D:\model\BAAI\bge-reranker-v2-m3"
    RERANKER_MODEL_NAME = "BGE-Reranker-v2-M3"
    RERANKER_TOP_N = 5
    
    # LLM配置
    LLM_API_KEY = "ms-6f7de516-7098-44af-969d-b1d28d65ec00"
    LLM_BASE_URL = "https://api-inference.modelscope.cn/v1/"
    LLM_MODEL_NAME = "Qwen/Qwen2.5-Coder-32B-Instruct"
    LLM_TIMEOUT = 30
    LLM_MAX_TOKENS = 2000
    LLM_TEMPERATURE = 0.7
    
    # ==================== 文件处理配置 ====================
    
    # 文本分割配置
    CHUNK_MIN_SIZE = 200
    CHUNK_MAX_SIZE = 800
    CHUNK_OVERLAP_SIZE = 100
    SENTENCE_OVERLAP = 2
    
    # OCR配置
    OCR_CONFIG = '--oem 3 --psm 6 -l chi_sim+eng'
    OCR_LANGUAGES = ['chi_sim', 'eng']
    
    # 表格检测配置
    TABLE_DETECTION_THRESHOLD = 0.7
    TABLE_MIN_ROWS = 2
    TABLE_MIN_COLS = 2
    
    # 图片处理配置
    IMAGE_MAX_SIZE = (2048, 2048)
    IMAGE_QUALITY = 85
    
    # ==================== 检索配置 ====================
    
    # 向量检索配置
    VECTOR_SEARCH_TOP_K = 15
    VECTOR_SEARCH_SCORE_THRESHOLD = 0.5
    
    # 混合检索配置
    HYBRID_SEARCH_ENABLED = True
    HYBRID_VECTOR_WEIGHT = 0.6
    HYBRID_KEYWORD_WEIGHT = 0.3
    HYBRID_SEMANTIC_WEIGHT = 0.1
    
    # 关键词检索配置
    KEYWORD_SEARCH_TOP_K = 20
    KEYWORD_MIN_LENGTH = 2
    KEYWORD_MAX_COUNT = 10
    
    # 语义检索配置
    SEMANTIC_SEARCH_TOP_K = 20
    SEMANTIC_SIMILARITY_THRESHOLD = 0.1
    
    # 缓存配置
    SEARCH_CACHE_SIZE = 1000
    SEARCH_CACHE_TTL = 3600  # 缓存生存时间（秒）
    
    # ==================== 语音处理配置 ====================
    
    # 语音识别配置
    SPEECH_RECOGNITION_LANGUAGE = "zh-CN"
    SPEECH_RECOGNITION_TIMEOUT = 30
    SPEECH_RECOGNITION_ENGINES = ["Google", "Google Cloud", "Sphinx"]
    
    # 语音合成配置
    TTS_RATE = 180          # 语音速度
    TTS_VOLUME = 0.8        # 音量
    TTS_MAX_TEXT_LENGTH = 500  # 最大文本长度
    
    # 音频格式配置
    AUDIO_FORMAT = 'wav'
    AUDIO_SAMPLE_RATE = 16000
    AUDIO_CHANNELS = 1
    
    # ==================== NER和分类配置 ====================
    
    # 命名实体识别配置
    NER_ENABLED = True
    NER_CONFIDENCE_THRESHOLD = 0.8
    
    # 问题分类配置
    QUESTION_CATEGORIES = {
        "财务查询": {
            "keywords": ["收入", "利润", "资产", "负债", "财务", "业绩", "盈利", "亏损"],
            "weight": 1.0
        },
        "风险分析": {
            "keywords": ["风险", "挑战", "威胁", "不确定性", "问题", "困难"],
            "weight": 1.0
        },
        "业务咨询": {
            "keywords": ["业务", "产品", "服务", "市场", "客户", "竞争", "模式"],
            "weight": 1.0
        },
        "投资分析": {
            "keywords": ["投资", "估值", "前景", "价值", "回报", "股价", "市值"],
            "weight": 1.0
        },
        "公司治理": {
            "keywords": ["治理", "管理", "董事", "股东", "控制", "决策", "制度"],
            "weight": 1.0
        },
        "行业分析": {
            "keywords": ["行业", "市场", "趋势", "发展", "前景", "环境", "政策"],
            "weight": 1.0
        },
        "技术创新": {
            "keywords": ["技术", "研发", "创新", "专利", "科技", "数字化", "智能"],
            "weight": 1.0
        },
        "一般咨询": {
            "keywords": ["什么", "如何", "为什么", "介绍", "说明", "解释"],
            "weight": 0.5
        }
    }
    
    # ==================== 性能优化配置 ====================
    
    # 并发配置
    MAX_WORKERS = 4
    ENABLE_PARALLEL_PROCESSING = True
    
    # 内存管理配置
    MAX_MEMORY_USAGE = 8 * 1024 * 1024 * 1024  # 8GB
    GARBAGE_COLLECTION_THRESHOLD = 1000
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE = 'app.log'
    LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5
    
    # ==================== 安全配置 ====================
    
    # 请求限制配置
    RATE_LIMIT_ENABLED = True
    RATE_LIMIT_REQUESTS = 100
    RATE_LIMIT_WINDOW = 3600  # 1小时
    
    # 文件安全配置
    SCAN_UPLOADED_FILES = True
    MAX_FILE_SIZE_SCAN = 100 * 1024 * 1024  # 100MB
    
    # ==================== 监控配置 ====================
    
    # 健康检查配置
    HEALTH_CHECK_ENABLED = True
    HEALTH_CHECK_INTERVAL = 60  # 秒
    
    # 性能监控配置
    PERFORMANCE_MONITORING = True
    METRICS_COLLECTION_INTERVAL = 300  # 5分钟
    
    # ==================== 开发配置 ====================
    
    # 调试配置
    ENABLE_PROFILING = False
    ENABLE_DETAILED_LOGGING = False
    
    # 测试配置
    TESTING = False
    TEST_DATABASE_URL = "sqlite:///test.db"
    
    @classmethod
    def get_config_dict(cls) -> Dict:
        """
        获取所有配置参数的字典
        
        Returns:
            配置参数字典
        """
        config_dict = {}
        for attr_name in dir(cls):
            if not attr_name.startswith('_') and not callable(getattr(cls, attr_name)):
                config_dict[attr_name] = getattr(cls, attr_name)
        return config_dict
    
    @classmethod
    def validate_config(cls) -> List[str]:
        """
        验证配置参数的有效性
        
        Returns:
            验证错误列表
        """
        errors = []
        
        # 检查必需的路径
        if not os.path.exists(cls.EMBEDDING_MODEL_PATH):
            errors.append(f"嵌入模型路径不存在: {cls.EMBEDDING_MODEL_PATH}")
        
        if not os.path.exists(cls.RERANKER_MODEL_PATH):
            errors.append(f"重排序模型路径不存在: {cls.RERANKER_MODEL_PATH}")
        
        # 检查数值范围
        if cls.CHUNK_MIN_SIZE >= cls.CHUNK_MAX_SIZE:
            errors.append("CHUNK_MIN_SIZE 必须小于 CHUNK_MAX_SIZE")
        
        if cls.CHUNK_OVERLAP_SIZE >= cls.CHUNK_MIN_SIZE:
            errors.append("CHUNK_OVERLAP_SIZE 必须小于 CHUNK_MIN_SIZE")
        
        # 检查权重总和
        total_weight = (cls.HYBRID_VECTOR_WEIGHT + 
                       cls.HYBRID_KEYWORD_WEIGHT + 
                       cls.HYBRID_SEMANTIC_WEIGHT)
        if abs(total_weight - 1.0) > 0.01:
            errors.append(f"混合检索权重总和应为1.0，当前为: {total_weight}")
        
        return errors
    
    @classmethod
    def update_config(cls, **kwargs):
        """
        动态更新配置参数
        
        Args:
            **kwargs: 要更新的配置参数
        """
        for key, value in kwargs.items():
            if hasattr(cls, key):
                setattr(cls, key, value)
            else:
                raise ValueError(f"未知的配置参数: {key}")

# 环境特定配置
class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    ENABLE_DETAILED_LOGGING = True
    ENABLE_PROFILING = True

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    ENABLE_DETAILED_LOGGING = False
    ENABLE_PROFILING = False
    LOG_LEVEL = 'WARNING'

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True
    MILVUS_COLLECTION_NAME = 'test_document_vectors'
    REDIS_DB = 1

# 根据环境变量选择配置
def get_config():
    """
    根据环境变量获取配置类
    
    Returns:
        配置类
    """
    env = os.environ.get('FLASK_ENV', 'development')
    
    if env == 'production':
        return ProductionConfig
    elif env == 'testing':
        return TestingConfig
    else:
        return DevelopmentConfig
