#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的主应用程序
确保所有语法正确，能够正常启动运行
"""

import os
import uuid
import logging
import time
from flask import Flask, request, jsonify, render_template, session
from flask_cors import CORS

# 设置基本日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入核心模块
try:
    from file_processor import allowed_file, process_uploaded_file
    from vector_search import search_similar
    from llm_client import generate_answer, init_llm_client
    from embeddings import generate_embeddings, init_embedding_model
    from milvus_manager import init_milvus, store_vectors
    from reranker import rerank_documents
    from redis_manager import redis_manager
    logger.info("所有核心模块导入成功")
except ImportError as e:
    logger.error(f"核心模块导入失败: {e}")
    raise

# 可选模块导入
try:
    from file_processor import process_uploaded_file_enhanced
    ENHANCED_PROCESSING = True
except ImportError:
    ENHANCED_PROCESSING = False
    logger.warning("增强文件处理模块不可用")

try:
    from vector_search import search_similar_enhanced
    ENHANCED_SEARCH = True
except ImportError:
    ENHANCED_SEARCH = False
    logger.warning("增强搜索模块不可用")

# 创建Flask应用
app = Flask(__name__)
app.secret_key = 'your-secret-key-here'
CORS(app)

# 配置
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'docx', 'doc', 'jpg', 'jpeg', 'png'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB

# 确保目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs("static/audio", exist_ok=True)
os.makedirs("logs", exist_ok=True)

# 全局组件
collection = None
embedding_model = None
llm_client = None

def initialize_application():
    """初始化应用程序组件"""
    global collection, embedding_model, llm_client
    
    try:
        logger.info("正在初始化应用程序组件...")
        
        # 初始化Milvus
        collection = init_milvus()
        logger.info("Milvus向量数据库初始化成功")
        
        # 初始化嵌入模型
        embedding_model = init_embedding_model()
        logger.info("嵌入模型初始化成功")
        
        # 初始化LLM客户端
        llm_client = init_llm_client()
        logger.info("LLM客户端初始化成功")
        
        logger.info("所有组件初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"组件初始化失败: {str(e)}", exc_info=True)
        return False

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """处理文件上传"""
    try:
        # 检查文件是否存在
        if 'file' not in request.files:
            return jsonify({"status": "error", "message": "没有文件部分"})

        file = request.files['file']
        if file.filename == '':
            return jsonify({"status": "error", "message": "没有选择文件"})

        # 检查文件类型
        if not file or not allowed_file(file.filename, ALLOWED_EXTENSIONS):
            return jsonify({
                "status": "error", 
                "message": f"不支持的文件类型。支持的类型: {', '.join(sorted(ALLOWED_EXTENSIONS))}"
            })

        # 检查文件大小
        file.seek(0, 2)
        file_size = file.tell()
        file.seek(0)
        
        if file_size > app.config['MAX_CONTENT_LENGTH']:
            return jsonify({
                "status": "error", 
                "message": f"文件过大，最大支持 {app.config['MAX_CONTENT_LENGTH'] // (1024*1024)}MB"
            })

        logger.info(f"开始处理文件上传: {file.filename}, 大小: {file_size} bytes")

        # 处理文件
        try:
            if ENHANCED_PROCESSING:
                result = process_uploaded_file_enhanced(
                    file, app.config['UPLOAD_FOLDER'], collection, generate_embeddings
                )
                processing_method = 'enhanced'
            else:
                result = process_uploaded_file(
                    file, app.config['UPLOAD_FOLDER'], collection, generate_embeddings
                )
                processing_method = 'standard'
            
            # 添加文件信息
            result['file_info'] = {
                'filename': file.filename,
                'file_size': file_size,
                'processing_method': processing_method
            }
            
            logger.info(f"文件处理成功: {file.filename}")
            return jsonify(result)
            
        except Exception as e:
            logger.error(f"文件处理失败: {str(e)}", exc_info=True)
            return jsonify({
                "status": "error", 
                "message": f"处理文件时出错: {str(e)}"
            })
        
    except Exception as e:
        logger.error(f"文件上传处理错误: {str(e)}", exc_info=True)
        return jsonify({"status": "error", "message": "文件上传处理失败"})

@app.route('/query', methods=['POST'])
def handle_query():
    """处理用户查询"""
    try:
        # 解析请求数据
        data = request.json
        if not data or 'query' not in data:
            return jsonify({"status": "error", "message": "缺少查询内容"})

        # 获取会话ID
        if 'session_id' not in session:
            session['session_id'] = str(uuid.uuid4())
        session_id = session['session_id']

        # 获取历史对话
        chat_history = redis_manager.get_session(session_id)

        query_text = data['query'].strip()
        if not query_text:
            return jsonify({"status": "error", "message": "查询内容不能为空"})

        logger.info(f"处理查询: {query_text[:50]}...")

        # 向量检索
        try:
            if ENHANCED_SEARCH:
                initial_results = search_similar_enhanced(
                    query_text, collection, generate_embeddings, top_k=15
                )
                search_method = "enhanced"
            else:
                initial_results = search_similar(
                    query_text, collection, generate_embeddings, top_k=15
                )
                search_method = "standard"
                
            logger.info(f"使用{search_method}检索，获得{len(initial_results)}个结果")
            
        except Exception as e:
            logger.error(f"检索失败: {str(e)}")
            initial_results = []

        # 检查是否有搜索结果
        if not initial_results:
            no_result_message = "没有找到相关信息来回答这个问题。请先上传相关文件。"
            
            # 添加到历史对话
            new_history = chat_history + [
                {"role": "user", "content": query_text},
                {"role": "assistant", "content": no_result_message}
            ]
            redis_manager.store_session(session_id, new_history)

            return jsonify({
                "status": "success",
                "session_id": session_id,
                "answer": no_result_message,
                "context": [],
                "search_method": search_method
            })

        # 文档重排序
        try:
            reranked_results = rerank_documents(query_text, initial_results, top_n=5)
            logger.info(f"重排序完成，选择{len(reranked_results)}个最相关文档")
        except Exception as e:
            logger.warning(f"重排序失败: {str(e)}")
            reranked_results = initial_results[:5]

        # 生成答案
        try:
            answer = generate_answer(query_text, reranked_results, llm_client, chat_history)
            logger.info("答案生成完成")
        except Exception as e:
            logger.error(f"答案生成失败: {str(e)}")
            answer = "抱歉，生成答案时出现错误，请稍后再试。"

        # 更新历史对话
        new_history = chat_history + [
            {"role": "user", "content": query_text},
            {"role": "assistant", "content": answer}
        ]
        redis_manager.store_session(session_id, new_history)

        # 准备响应数据
        response_data = {
            "status": "success",
            "session_id": session_id,
            "answer": answer,
            "context": [doc.get("text", "") for doc in reranked_results[:3]],
            "search_method": search_method,
            "result_count": len(initial_results)
        }

        # 添加参考文档信息
        if reranked_results:
            references = []
            for doc in reranked_results[:5]:
                ref = {
                    "text": doc.get("text", "")[:300] + "..." if len(doc.get("text", "")) > 300 else doc.get("text", ""),
                    "score": doc.get("score", 0)
                }
                references.append(ref)
            
            response_data["references"] = references

        logger.info(f"查询处理完成: {session_id}")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"查询处理错误: {str(e)}", exc_info=True)
        return jsonify({"status": "error", "message": "查询处理失败"})

@app.route('/system-status', methods=['GET'])
def system_status():
    """获取系统状态"""
    try:
        status_data = {
            "timestamp": time.time(),
            "components": {
                "milvus": collection is not None,
                "embedding_model": embedding_model is not None,
                "llm_client": llm_client is not None,
                "redis": redis_manager is not None
            },
            "features": {
                "enhanced_processing": ENHANCED_PROCESSING,
                "enhanced_search": ENHANCED_SEARCH
            }
        }
        
        return jsonify({
            "status": "success",
            "data": status_data
        })
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}", exc_info=True)
        return jsonify({"status": "error", "message": "获取系统状态失败"})

@app.errorhandler(404)
def not_found_error(error):
    """处理404错误"""
    return jsonify({
        "status": "error",
        "message": "请求的资源不存在",
        "error_code": 404
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """处理500错误"""
    logger.error(f"服务器内部错误: {error}")
    return jsonify({
        "status": "error",
        "message": "服务器内部错误",
        "error_code": 500
    }), 500

@app.errorhandler(413)
def file_too_large(error):
    """处理文件过大错误"""
    return jsonify({
        "status": "error",
        "message": f"文件过大，最大支持 {app.config['MAX_CONTENT_LENGTH'] // (1024*1024)}MB",
        "error_code": 413
    }), 413

def create_app():
    """应用工厂函数"""
    if not initialize_application():
        logger.error("应用初始化失败")
        return None
    
    logger.info("应用创建完成")
    return app

if __name__ == '__main__':
    try:
        # 创建应用
        application = create_app()
        if application is None:
            logger.error("应用创建失败，退出")
            exit(1)
        
        logger.info("启动服务器: 0.0.0.0:5000")
        logger.info("访问地址: http://localhost:5000")
        
        # 启动应用
        application.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            threaded=True
        )
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭应用...")
        
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        exit(1)
