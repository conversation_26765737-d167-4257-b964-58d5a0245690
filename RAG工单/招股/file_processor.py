import os
import re
import fitz  # PyMuPDF for PDF processing
import logging
from PIL import Image
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional, Union
from io import BytesIO
import base64
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# 可选导入，避免导入错误
try:
    import pytesseract
    PYTESSERACT_AVAILABLE = True
except ImportError:
    PYTESSERACT_AVAILABLE = False
    logging.warning("pytesseract未安装，OCR功能将被禁用")

try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    logging.warning("opencv-python未安装，图像处理功能将受限")

try:
    import camelot
    CAMELOT_AVAILABLE = True
except ImportError:
    CAMELOT_AVAILABLE = False
    logging.warning("camelot-py未安装，PDF表格提取功能将受限")

try:
    import tabula
    TABULA_AVAILABLE = True
except ImportError:
    TABULA_AVAILABLE = False
    logging.warning("tabula-py未安装，PDF表格提取功能将受限")


def allowed_file(filename: str, allowed_extensions: set) -> bool:
    """
    检查文件是否为允许的类型

    Args:
        filename: 文件名
        allowed_extensions: 允许的扩展名集合

    Returns:
        是否为允许的文件类型
    """
    return '.' in filename and \
        filename.rsplit('.', 1)[1].lower() in allowed_extensions

class EnhancedFileProcessor:
    """
    增强的文件处理器
    支持PDF、图片、表格等多种格式的智能处理
    """

    def __init__(self):
        """
        初始化文件处理器
        """
        self.logger = logging.getLogger(__name__)

        # 支持的文件类型
        self.supported_extensions = {
            'pdf', 'txt', 'docx', 'doc',  # 文档
            'jpg', 'jpeg', 'png', 'bmp', 'tiff',  # 图片
            'xlsx', 'xls', 'csv'  # 表格
        }

        # OCR配置
        self.ocr_config = '--oem 3 --psm 6 -l chi_sim+eng'
        self.ocr_available = PYTESSERACT_AVAILABLE
        self.cv2_available = CV2_AVAILABLE

        # 表格检测阈值
        self.table_detection_threshold = 0.7

        self.logger.info("增强文件处理器初始化完成")

    def is_audio_file(self, filename: str) -> bool:
        """
        检查是否为音频文件

        Args:
            filename: 文件名

        Returns:
            是否为音频文件
        """
        audio_extensions = {'wav', 'mp3', 'm4a', 'flac', 'aac', 'ogg', 'wma'}
        ext = filename.rsplit('.', 1)[-1].lower() if '.' in filename else ''
        return ext in audio_extensions

    def get_file_type(self, filename: str) -> str:
        """
        获取文件类型

        Args:
            filename: 文件名

        Returns:
            文件类型: document, image, table, unknown
        """
        ext = filename.rsplit('.', 1)[-1].lower() if '.' in filename else ''

        if ext in {'pdf', 'txt', 'docx', 'doc'}:
            return 'document'
        elif ext in {'jpg', 'jpeg', 'png', 'bmp', 'tiff'}:
            return 'image'
        elif ext in {'xlsx', 'xls', 'csv'}:
            return 'table'
        else:
            return 'unknown'


    def process_pdf_enhanced(self, file_path: str) -> Dict[str, any]:
        """
        增强的PDF处理功能
        支持文本提取、表格识别、图片OCR等

        Args:
            file_path: PDF文件路径

        Returns:
            处理结果字典，包含文本、表格、图片等信息
        """
        try:
            doc = fitz.open(file_path)
            result = {
                "text": "",
                "tables": [],
                "images": [],
                "metadata": {},
                "page_count": len(doc)
            }

            # 获取文档元数据
            result["metadata"] = self._extract_pdf_metadata(doc)

            # 并行处理页面
            with ThreadPoolExecutor(max_workers=4) as executor:
                future_to_page = {
                    executor.submit(self._process_pdf_page, doc, page_num): page_num
                    for page_num in range(len(doc))
                }

                for future in as_completed(future_to_page):
                    page_num = future_to_page[future]
                    try:
                        page_result = future.result()

                        # 合并页面结果
                        result["text"] += f"\n\n--- 第{page_num + 1}页 ---\n\n"
                        result["text"] += page_result["text"]
                        result["tables"].extend(page_result["tables"])
                        result["images"].extend(page_result["images"])

                    except Exception as e:
                        self.logger.error(f"处理第{page_num + 1}页失败: {str(e)}")

            # 清理和优化文本
            result["text"] = self._clean_extracted_text(result["text"])

            # 提取表格数据
            if result["tables"]:
                result["table_text"] = self._extract_table_text(result["tables"])

            doc.close()

            self.logger.info(f"PDF处理完成: {result['page_count']}页, "
                           f"{len(result['tables'])}个表格, "
                           f"{len(result['images'])}张图片")

            return result

        except Exception as e:
            self.logger.error(f"PDF处理失败: {str(e)}")
            raise Exception(f"处理PDF错误: {str(e)}")

    def _extract_pdf_metadata(self, doc) -> Dict[str, any]:
        """
        提取PDF元数据

        Args:
            doc: PyMuPDF文档对象

        Returns:
            元数据字典
        """
        try:
            metadata = doc.metadata
            return {
                "title": metadata.get("title", ""),
                "author": metadata.get("author", ""),
                "subject": metadata.get("subject", ""),
                "creator": metadata.get("creator", ""),
                "producer": metadata.get("producer", ""),
                "creation_date": metadata.get("creationDate", ""),
                "modification_date": metadata.get("modDate", ""),
                "page_count": len(doc)
            }
        except Exception as e:
            self.logger.warning(f"提取PDF元数据失败: {str(e)}")
            return {}

    def _process_pdf_page(self, doc, page_num: int) -> Dict[str, any]:
        """
        处理单个PDF页面

        Args:
            doc: PyMuPDF文档对象
            page_num: 页面编号

        Returns:
            页面处理结果
        """
        try:
            page = doc[page_num]
            result = {
                "text": "",
                "tables": [],
                "images": []
            }

            # 1. 提取文本
            page_text = page.get_text("text")

            # 2. 检测和提取表格
            tables = self._detect_tables_in_page(page)
            result["tables"] = tables

            # 3. 提取图片并进行OCR
            images = self._extract_images_from_page(page, page_num)
            result["images"] = images

            # 4. 如果文本很少，使用OCR
            if len(page_text.strip()) < 100:
                ocr_text = self._ocr_page(page)
                if ocr_text:
                    page_text = ocr_text

            result["text"] = page_text

            return result

        except Exception as e:
            self.logger.error(f"处理页面{page_num + 1}失败: {str(e)}")
            return {"text": "", "tables": [], "images": []}

    def _detect_tables_in_page(self, page) -> List[Dict]:
        """
        检测页面中的表格

        Args:
            page: PyMuPDF页面对象

        Returns:
            表格列表
        """
        tables = []
        try:
            # 方法1: 使用PyMuPDF的表格检测
            try:
                tabs = page.find_tables()
                for tab in tabs:
                    try:
                        table_data = tab.extract()
                        if table_data and len(table_data) > 1:  # 至少有2行
                            tables.append({
                                "data": table_data,
                                "bbox": tab.bbox,
                                "method": "pymupdf",
                                "confidence": 0.8
                            })
                    except Exception as e:
                        self.logger.warning(f"表格提取失败: {str(e)}")
            except AttributeError:
                # 如果PyMuPDF版本不支持find_tables，使用文本模式检测
                text = page.get_text()
                if self._detect_table_pattern(text):
                    tables.append({
                        "data": [["检测到表格内容", "但无法提取具体数据"]],
                        "bbox": None,
                        "method": "text_pattern",
                        "confidence": 0.5
                    })

            return tables

        except Exception as e:
            self.logger.warning(f"表格检测失败: {str(e)}")
            return []

    def _detect_table_pattern(self, text: str) -> bool:
        """
        通过文本模式检测表格

        Args:
            text: 页面文本

        Returns:
            是否包含表格
        """
        # 简单的表格模式检测
        lines = text.split('\n')
        table_indicators = 0

        for line in lines:
            # 检测制表符或多个空格分隔的内容
            if '\t' in line or re.search(r'\s{3,}', line):
                table_indicators += 1
            # 检测表格关键词
            if any(keyword in line for keyword in ['表', '项目', '金额', '比例', '年度']):
                table_indicators += 1

        return table_indicators > 3

    def _extract_images_from_page(self, page, page_num: int) -> List[Dict]:
        """
        从页面提取图片并进行OCR

        Args:
            page: PyMuPDF页面对象
            page_num: 页面编号

        Returns:
            图片信息列表
        """
        images = []
        try:
            image_list = page.get_images()

            for img_index, img in enumerate(image_list):
                try:
                    # 获取图片数据
                    xref = img[0]
                    pix = fitz.Pixmap(page.parent, xref)

                    # 转换为PIL图片
                    if pix.n - pix.alpha < 4:  # GRAY or RGB
                        img_data = pix.tobytes("png")
                        pil_img = Image.open(BytesIO(img_data))

                        # 图片预处理
                        processed_img = self._preprocess_image_for_ocr(pil_img)

                        # OCR识别
                        ocr_text = self._ocr_image(processed_img)

                        if ocr_text and len(ocr_text.strip()) > 10:
                            images.append({
                                "page": page_num + 1,
                                "index": img_index,
                                "text": ocr_text,
                                "size": (pix.width, pix.height),
                                "confidence": self._calculate_ocr_confidence(ocr_text)
                            })

                    pix = None  # 释放内存

                except Exception as e:
                    self.logger.warning(f"处理图片{img_index}失败: {str(e)}")

            return images

        except Exception as e:
            self.logger.warning(f"图片提取失败: {str(e)}")
            return []

    def _preprocess_image_for_ocr(self, image: Image.Image) -> Image.Image:
        """
        图片预处理以提高OCR准确率

        Args:
            image: PIL图片对象

        Returns:
            预处理后的图片
        """
        try:
            if not self.cv2_available:
                # 如果没有cv2，进行简单的预处理
                if image.mode != 'L':
                    image = image.convert('L')  # 转为灰度图
                return image

            # 转换为numpy数组
            img_array = np.array(image)

            # 转换为灰度图
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array

            # 去噪
            denoised = cv2.medianBlur(gray, 3)

            # 二值化
            _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 形态学操作
            kernel = np.ones((1, 1), np.uint8)
            processed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

            # 转换回PIL图片
            return Image.fromarray(processed)

        except Exception as e:
            self.logger.warning(f"图片预处理失败: {str(e)}")
            return image

    def _ocr_image(self, image: Image.Image) -> str:
        """
        对图片进行OCR识别

        Args:
            image: PIL图片对象

        Returns:
            识别出的文字
        """
        try:
            if not self.ocr_available:
                self.logger.warning("OCR功能不可用，pytesseract未安装")
                return ""

            # 使用pytesseract进行OCR
            text = pytesseract.image_to_string(image, config=self.ocr_config)

            # 清理OCR结果
            text = self._clean_ocr_text(text)

            return text

        except Exception as e:
            self.logger.warning(f"OCR识别失败: {str(e)}")
            return ""

    def _clean_ocr_text(self, text: str) -> str:
        """
        清理OCR识别结果

        Args:
            text: 原始OCR文本

        Returns:
            清理后的文本
        """
        if not text:
            return ""

        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)

        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff.,;:!?()[\]{}""''—-]', '', text)

        # 移除过短的行
        lines = text.split('\n')
        cleaned_lines = [line.strip() for line in lines if len(line.strip()) > 2]

        return '\n'.join(cleaned_lines).strip()

    def _calculate_ocr_confidence(self, text: str) -> float:
        """
        计算OCR识别置信度

        Args:
            text: OCR识别文本

        Returns:
            置信度分数 (0-1)
        """
        if not text:
            return 0.0

        # 基于文本特征计算置信度
        score = 0.5  # 基础分数

        # 文本长度
        if len(text) > 20:
            score += 0.1
        if len(text) > 50:
            score += 0.1

        # 中文字符比例
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        if chinese_chars > 0:
            chinese_ratio = chinese_chars / len(text)
            score += chinese_ratio * 0.2

        # 数字和标点符号
        if re.search(r'\d', text):
            score += 0.1
        if re.search(r'[.,;:!?]', text):
            score += 0.1

        return min(score, 1.0)

    def _ocr_page(self, page) -> str:
        """
        对整个页面进行OCR

        Args:
            page: PyMuPDF页面对象

        Returns:
            OCR识别文本
        """
        try:
            # 获取页面图片
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2倍缩放提高质量
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

            # 预处理
            processed_img = self._preprocess_image_for_ocr(img)

            # OCR识别
            text = self._ocr_image(processed_img)

            return text

        except Exception as e:
            self.logger.warning(f"页面OCR失败: {str(e)}")
            return ""

    def _extract_table_text(self, tables: List[Dict]) -> str:
        """
        从表格数据中提取文本

        Args:
            tables: 表格列表

        Returns:
            表格文本内容
        """
        table_texts = []

        for i, table in enumerate(tables):
            try:
                table_data = table.get("data", [])
                if not table_data:
                    continue

                # 转换为DataFrame进行处理
                df = pd.DataFrame(table_data)

                # 清理数据
                df = df.fillna("")  # 填充空值
                df = df.astype(str)  # 转换为字符串

                # 生成表格描述文本
                table_text = f"\n\n=== 表格 {i + 1} ===\n"

                # 添加表头
                if len(df) > 0:
                    headers = df.iloc[0].tolist()
                    table_text += "表头: " + " | ".join(headers) + "\n"

                    # 添加数据行
                    for idx, row in df.iloc[1:].iterrows():
                        row_text = " | ".join(row.tolist())
                        table_text += f"行{idx}: {row_text}\n"

                table_texts.append(table_text)

            except Exception as e:
                self.logger.warning(f"处理表格{i}失败: {str(e)}")

        return "\n".join(table_texts)

    def _clean_extracted_text(self, text: str) -> str:
        """
        清理提取的文本

        Args:
            text: 原始文本

        Returns:
            清理后的文本
        """
        if not text:
            return ""

        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)

        # 移除重复的分页符
        text = re.sub(r'(--- 第\d+页 ---\s*){2,}', r'--- 页面分隔 ---\n', text)

        # 移除过短的行
        lines = text.split('\n')
        cleaned_lines = []
        for line in lines:
            line = line.strip()
            if len(line) > 2 or line.startswith('---'):
                cleaned_lines.append(line)

        # 合并相似的行
        merged_lines = self._merge_similar_lines(cleaned_lines)

        return '\n'.join(merged_lines).strip()

    def _merge_similar_lines(self, lines: List[str]) -> List[str]:
        """
        合并相似的文本行

        Args:
            lines: 文本行列表

        Returns:
            合并后的文本行
        """
        if not lines:
            return []

        merged = []
        current_line = lines[0]

        for next_line in lines[1:]:
            # 计算相似度
            similarity = self._calculate_line_similarity(current_line, next_line)

            if similarity > 0.8 and len(current_line) < 100:
                # 合并相似行
                current_line = self._merge_lines(current_line, next_line)
            else:
                merged.append(current_line)
                current_line = next_line

        merged.append(current_line)
        return merged

    def _calculate_line_similarity(self, line1: str, line2: str) -> float:
        """
        计算两行文本的相似度

        Args:
            line1: 第一行文本
            line2: 第二行文本

        Returns:
            相似度分数 (0-1)
        """
        if not line1 or not line2:
            return 0.0

        # 简单的字符重叠率计算
        set1 = set(line1.lower())
        set2 = set(line2.lower())

        intersection = len(set1 & set2)
        union = len(set1 | set2)

        return intersection / union if union > 0 else 0.0

    def _merge_lines(self, line1: str, line2: str) -> str:
        """
        合并两行文本

        Args:
            line1: 第一行文本
            line2: 第二行文本

        Returns:
            合并后的文本
        """
        # 如果一行是另一行的子集，返回较长的行
        if line1 in line2:
            return line2
        elif line2 in line1:
            return line1
        else:
            # 否则连接两行
            return f"{line1} {line2}"

    def process_image_file(self, file_path: str) -> Dict[str, any]:
        """
        处理图片文件

        Args:
            file_path: 图片文件路径

        Returns:
            处理结果
        """
        try:
            # 打开图片
            image = Image.open(file_path)

            # 获取图片信息
            img_info = {
                "size": image.size,
                "mode": image.mode,
                "format": image.format
            }

            # 预处理图片
            processed_img = self._preprocess_image_for_ocr(image)

            # OCR识别
            text = self._ocr_image(processed_img)

            result = {
                "text": text,
                "image_info": img_info,
                "confidence": self._calculate_ocr_confidence(text)
            }

            self.logger.info(f"图片处理完成: {file_path}")
            return result

        except Exception as e:
            self.logger.error(f"图片处理失败: {str(e)}")
            raise Exception(f"处理图片错误: {str(e)}")

    def process_table_file(self, file_path: str) -> Dict[str, any]:
        """
        处理表格文件 (Excel, CSV)

        Args:
            file_path: 表格文件路径

        Returns:
            处理结果
        """
        try:
            # 根据文件类型读取
            if file_path.lower().endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8')
            else:
                df = pd.read_excel(file_path)

            # 生成文本描述
            text = self._dataframe_to_text(df)

            result = {
                "text": text,
                "table_info": {
                    "rows": len(df),
                    "columns": len(df.columns),
                    "columns_names": df.columns.tolist()
                }
            }

            self.logger.info(f"表格处理完成: {file_path}")
            return result

        except Exception as e:
            self.logger.error(f"表格处理失败: {str(e)}")
            raise Exception(f"处理表格错误: {str(e)}")

    def _dataframe_to_text(self, df: pd.DataFrame) -> str:
        """
        将DataFrame转换为文本描述

        Args:
            df: pandas DataFrame

        Returns:
            文本描述
        """
        try:
            text_parts = []

            # 添加表格基本信息
            text_parts.append(f"表格包含 {len(df)} 行 {len(df.columns)} 列")
            text_parts.append(f"列名: {', '.join(df.columns.tolist())}")

            # 添加数据摘要
            text_parts.append("\n数据内容:")

            # 逐行添加数据
            for idx, row in df.head(100).iterrows():  # 限制前100行
                row_text = []
                for col, value in row.items():
                    if pd.notna(value):
                        row_text.append(f"{col}: {value}")

                if row_text:
                    text_parts.append(f"第{idx + 1}行 - " + ", ".join(row_text))

            if len(df) > 100:
                text_parts.append(f"... (还有 {len(df) - 100} 行数据)")

            return "\n".join(text_parts)

        except Exception as e:
            self.logger.error(f"DataFrame转文本失败: {str(e)}")
            return str(df)


def process_txt(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            text = f.read()
        # 清理文本
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    except Exception as e:
        raise Exception(f"处理TXT错误: {str(e)}")


class SmartTextChunker:
    """
    智能文本分割器
    保持语义连贯性、上下文联系和文档完整性
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 分割参数
        self.min_chunk_size = 200
        self.max_chunk_size = 800
        self.overlap_size = 100
        self.sentence_overlap = 2  # 句子重叠数量

        # 语义边界标识符
        self.strong_boundaries = [
            r'\n\s*第[一二三四五六七八九十\d]+章',  # 章节
            r'\n\s*第[一二三四五六七八九十\d]+节',  # 节
            r'\n\s*\d+\.\s*',  # 数字标题
            r'\n\s*[一二三四五六七八九十]+、',  # 中文序号
            r'\n\s*\([一二三四五六七八九十\d]+\)',  # 括号序号
        ]

        self.weak_boundaries = [
            r'\n\s*',  # 段落分隔
            r'[。！？]',  # 句子结束
            r'[；;]',  # 分号
        ]

        # 表格和图片标识符
        self.table_patterns = [
            r'表\s*\d+',
            r'Table\s*\d+',
            r'=== 表格 \d+ ===',
        ]

        self.figure_patterns = [
            r'图\s*\d+',
            r'Figure\s*\d+',
            r'附图\s*\d+',
        ]

    def chunk_text_smart(self, text: str, preserve_tables: bool = True,
                        preserve_context: bool = True) -> List[Dict[str, any]]:
        """
        智能文本分割

        Args:
            text: 输入文本
            preserve_tables: 是否保持表格完整性
            preserve_context: 是否保持上下文连贯性

        Returns:
            分割后的文本块列表
        """
        try:
            if not text or len(text.strip()) < self.min_chunk_size:
                return [{"text": text, "metadata": {"chunk_id": 0, "type": "single"}}]

            # 预处理文本
            processed_text = self._preprocess_text(text)

            # 识别特殊区域（表格、图片等）
            special_regions = self._identify_special_regions(processed_text)

            # 基于语义边界进行初步分割
            initial_chunks = self._split_by_semantic_boundaries(processed_text, special_regions)

            # 优化分块大小
            optimized_chunks = self._optimize_chunk_sizes(initial_chunks)

            # 添加重叠和上下文
            if preserve_context:
                final_chunks = self._add_context_overlap(optimized_chunks)
            else:
                final_chunks = optimized_chunks

            # 添加元数据
            chunks_with_metadata = self._add_chunk_metadata(final_chunks, special_regions)

            self.logger.info(f"智能分割完成: {len(chunks_with_metadata)} 个文本块")
            return chunks_with_metadata

        except Exception as e:
            self.logger.error(f"智能分割失败: {str(e)}")
            # 回退到简单分割
            return self._simple_chunk_fallback(text)

    def _preprocess_text(self, text: str) -> str:
        """
        预处理文本

        Args:
            text: 原始文本

        Returns:
            预处理后的文本
        """
        # 标准化换行符
        text = re.sub(r'\r\n|\r', '\n', text)

        # 移除多余的空白字符，但保留段落结构
        text = re.sub(r'[ \t]+', ' ', text)  # 合并空格和制表符
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)  # 合并多个空行为两个

        # 保护重要的格式标记
        text = re.sub(r'(\n=== .+ ===\n)', r'\1', text)  # 保护表格标记

        return text.strip()

    def _identify_special_regions(self, text: str) -> List[Dict[str, any]]:
        """
        识别特殊区域（表格、图片等）

        Args:
            text: 文本内容

        Returns:
            特殊区域列表
        """
        regions = []

        # 识别表格区域
        for pattern in self.table_patterns:
            for match in re.finditer(pattern, text, re.IGNORECASE):
                # 查找表格内容的结束位置
                start = match.start()
                end = self._find_table_end(text, start)

                regions.append({
                    "type": "table",
                    "start": start,
                    "end": end,
                    "content": text[start:end]
                })

        # 识别图片区域
        for pattern in self.figure_patterns:
            for match in re.finditer(pattern, text, re.IGNORECASE):
                start = match.start()
                end = self._find_figure_end(text, start)

                regions.append({
                    "type": "figure",
                    "start": start,
                    "end": end,
                    "content": text[start:end]
                })

        # 按位置排序
        regions.sort(key=lambda x: x["start"])

        return regions

    def _find_table_end(self, text: str, start: int) -> int:
        """
        查找表格内容的结束位置

        Args:
            text: 文本内容
            start: 表格开始位置

        Returns:
            表格结束位置
        """
        # 查找下一个段落或章节标题
        search_text = text[start:]

        # 查找强边界
        for pattern in self.strong_boundaries:
            match = re.search(pattern, search_text[100:])  # 跳过前100字符
            if match:
                return start + 100 + match.start()

        # 查找连续的空行
        match = re.search(r'\n\s*\n\s*\n', search_text[50:])
        if match:
            return start + 50 + match.start()

        # 默认返回500字符后的位置
        return min(start + 500, len(text))

    def _find_figure_end(self, text: str, start: int) -> int:
        """
        查找图片内容的结束位置

        Args:
            text: 文本内容
            start: 图片开始位置

        Returns:
            图片结束位置
        """
        # 图片通常比表格短
        search_text = text[start:]

        # 查找下一行或段落
        match = re.search(r'\n\s*\n', search_text[20:])
        if match:
            return start + 20 + match.start()

        return min(start + 200, len(text))

    def _split_by_semantic_boundaries(self, text: str, special_regions: List[Dict]) -> List[str]:
        """
        基于语义边界进行分割

        Args:
            text: 文本内容
            special_regions: 特殊区域列表

        Returns:
            初步分割的文本块
        """
        chunks = []
        current_pos = 0

        # 首先按强边界分割
        strong_splits = []
        for pattern in self.strong_boundaries:
            for match in re.finditer(pattern, text):
                strong_splits.append(match.start())

        strong_splits = sorted(set(strong_splits))
        strong_splits.append(len(text))  # 添加文本结束位置

        # 在强边界之间进行细分
        for i, split_pos in enumerate(strong_splits):
            if i == 0:
                chunk_text = text[current_pos:split_pos]
            else:
                chunk_text = text[current_pos:split_pos]

            if len(chunk_text.strip()) > 0:
                # 检查是否需要进一步分割
                if len(chunk_text) > self.max_chunk_size:
                    sub_chunks = self._split_large_chunk(chunk_text)
                    chunks.extend(sub_chunks)
                else:
                    chunks.append(chunk_text)

            current_pos = split_pos

        return [chunk for chunk in chunks if len(chunk.strip()) > 0]

    def _split_large_chunk(self, text: str) -> List[str]:
        """
        分割过大的文本块

        Args:
            text: 大文本块

        Returns:
            分割后的文本块列表
        """
        chunks = []
        current_chunk = ""

        # 按句子分割
        sentences = re.split(r'([。！？.!?])', text)

        i = 0
        while i < len(sentences):
            sentence = sentences[i]

            # 如果是标点符号，与前一个句子合并
            if i + 1 < len(sentences) and sentences[i + 1] in '。！？.!?':
                sentence += sentences[i + 1]
                i += 2
            else:
                i += 1

            # 检查添加句子后是否超过最大长度
            if len(current_chunk + sentence) > self.max_chunk_size and current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = sentence
            else:
                current_chunk += sentence

        # 添加最后一个块
        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        return chunks

    def _optimize_chunk_sizes(self, chunks: List[str]) -> List[str]:
        """
        优化文本块大小

        Args:
            chunks: 初步分割的文本块

        Returns:
            优化后的文本块
        """
        optimized = []
        i = 0

        while i < len(chunks):
            current_chunk = chunks[i]

            # 如果当前块太小，尝试与下一块合并
            if len(current_chunk) < self.min_chunk_size and i + 1 < len(chunks):
                next_chunk = chunks[i + 1]

                # 检查合并后是否超过最大长度
                if len(current_chunk + "\n" + next_chunk) <= self.max_chunk_size:
                    merged_chunk = current_chunk + "\n" + next_chunk
                    optimized.append(merged_chunk)
                    i += 2  # 跳过下一个块
                    continue

            optimized.append(current_chunk)
            i += 1

        return optimized

    def _add_context_overlap(self, chunks: List[str]) -> List[str]:
        """
        添加上下文重叠

        Args:
            chunks: 文本块列表

        Returns:
            添加重叠后的文本块
        """
        if len(chunks) <= 1:
            return chunks

        overlapped_chunks = []

        for i, chunk in enumerate(chunks):
            enhanced_chunk = chunk

            # 添加前文重叠
            if i > 0:
                prev_chunk = chunks[i - 1]
                prev_sentences = self._get_last_sentences(prev_chunk, self.sentence_overlap)
                if prev_sentences:
                    enhanced_chunk = prev_sentences + "\n" + enhanced_chunk

            # 添加后文重叠
            if i < len(chunks) - 1:
                next_chunk = chunks[i + 1]
                next_sentences = self._get_first_sentences(next_chunk, self.sentence_overlap)
                if next_sentences:
                    enhanced_chunk = enhanced_chunk + "\n" + next_sentences

            overlapped_chunks.append(enhanced_chunk)

        return overlapped_chunks

    def _get_last_sentences(self, text: str, count: int) -> str:
        """
        获取文本的最后几个句子

        Args:
            text: 文本内容
            count: 句子数量

        Returns:
            最后几个句子
        """
        sentences = re.split(r'[。！？.!?]', text)
        sentences = [s.strip() for s in sentences if s.strip()]

        if len(sentences) <= count:
            return text

        return '。'.join(sentences[-count:]) + '。'

    def _get_first_sentences(self, text: str, count: int) -> str:
        """
        获取文本的前几个句子

        Args:
            text: 文本内容
            count: 句子数量

        Returns:
            前几个句子
        """
        sentences = re.split(r'[。！？.!?]', text)
        sentences = [s.strip() for s in sentences if s.strip()]

        if len(sentences) <= count:
            return text

        return '。'.join(sentences[:count]) + '。'

    def _add_chunk_metadata(self, chunks: List[str], special_regions: List[Dict]) -> List[Dict[str, any]]:
        """
        为文本块添加元数据

        Args:
            chunks: 文本块列表
            special_regions: 特殊区域列表

        Returns:
            带元数据的文本块列表
        """
        chunks_with_metadata = []

        for i, chunk in enumerate(chunks):
            metadata = {
                "chunk_id": i,
                "length": len(chunk),
                "type": "text",
                "has_table": False,
                "has_figure": False,
                "semantic_level": self._assess_semantic_level(chunk)
            }

            # 检查是否包含特殊内容
            for region in special_regions:
                if region["content"] in chunk:
                    if region["type"] == "table":
                        metadata["has_table"] = True
                        metadata["type"] = "mixed" if metadata["type"] == "text" else "table"
                    elif region["type"] == "figure":
                        metadata["has_figure"] = True
                        metadata["type"] = "mixed" if metadata["type"] == "text" else "figure"

            chunks_with_metadata.append({
                "text": chunk,
                "metadata": metadata
            })

        return chunks_with_metadata

    def _assess_semantic_level(self, text: str) -> str:
        """
        评估文本的语义层级

        Args:
            text: 文本内容

        Returns:
            语义层级: title, section, paragraph, detail
        """
        # 检查是否为标题
        if re.match(r'^\s*第[一二三四五六七八九十\d]+章', text):
            return "title"
        elif re.match(r'^\s*第[一二三四五六七八九十\d]+节', text):
            return "section"
        elif re.match(r'^\s*\d+\.\s*', text):
            return "section"
        elif len(text) < 100:
            return "paragraph"
        else:
            return "detail"

    def _simple_chunk_fallback(self, text: str) -> List[Dict[str, any]]:
        """
        简单分割回退方案

        Args:
            text: 文本内容

        Returns:
            简单分割的文本块
        """
        chunks = []
        words = text.split()
        current_chunk = []
        current_length = 0

        for word in words:
            if current_length + len(word) > self.max_chunk_size and current_chunk:
                chunk_text = ' '.join(current_chunk)
                chunks.append({
                    "text": chunk_text,
                    "metadata": {"chunk_id": len(chunks), "type": "fallback"}
                })
                current_chunk = [word]
                current_length = len(word)
            else:
                current_chunk.append(word)
                current_length += len(word) + 1

        if current_chunk:
            chunk_text = ' '.join(current_chunk)
            chunks.append({
                "text": chunk_text,
                "metadata": {"chunk_id": len(chunks), "type": "fallback"}
            })

        return chunks

# 保持向后兼容的简单分割函数
def chunk_text(text, min_chunk_size=200, max_chunk_size=500, overlap=50):
    """
    按段落切分文本并加入重叠

    参数:
        text: 原始文本
        min_chunk_size: 最小分块大小(字符数)
        max_chunk_size: 最大分块大小(字符数)
        overlap: 重叠区域大小(字符数)

    返回:
        分块后的文本列表
    """
    # 按段落分割（考虑多种换行符）
    paragraphs = [p.strip() for p in re.split(r'\n\s*\n', text) if p.strip()]

    chunks = []
    current_chunk = ""

    for para in paragraphs:
        # 如果当前段落很短，直接添加到当前块
        if len(current_chunk) + len(para) < min_chunk_size:
            if current_chunk:
                current_chunk += "\n\n"
            current_chunk += para
            continue

        # 如果当前段落能直接加入当前块
        if len(current_chunk) + len(para) <= max_chunk_size:
            if current_chunk:
                current_chunk += "\n\n"
            current_chunk += para
        else:
            # 处理长段落
            start_idx = 0
            while start_idx < len(para):
                # 计算可以添加到当前块的文本量
                remaining_space = max_chunk_size - len(current_chunk) - 2  # 减去换行符

                if remaining_space > 0:
                    # 添加部分段落到当前块
                    end_idx = min(start_idx + remaining_space, len(para))
                    if current_chunk:
                        current_chunk += "\n\n"
                    current_chunk += para[start_idx:end_idx]
                    start_idx = end_idx

                # 如果当前块达到最大大小，保存它
                if len(current_chunk) >= min_chunk_size:
                    chunks.append(current_chunk)

                    # 创建重叠的新块
                    if overlap > 0 and chunks:
                        last_chunk = chunks[-1]
                        overlap_start = max(0, len(last_chunk) - overlap)
                        current_chunk = last_chunk[overlap_start:]
                    else:
                        current_chunk = ""

                # 如果剩余段落长度超过最大块大小，直接分割
                if len(para) - start_idx > max_chunk_size:
                    end_idx = min(start_idx + max_chunk_size, len(para))
                    chunks.append(para[start_idx:end_idx])
                    start_idx = end_idx

    # 添加最后一个块
    if current_chunk:
        chunks.append(current_chunk)

    return chunks


def process_uploaded_file_enhanced(file, upload_folder, collection, embedding_generator):
    """
    增强的文件处理函数
    支持多种文件类型和智能处理

    Args:
        file: 上传的文件对象
        upload_folder: 上传文件夹
        collection: Milvus集合
        embedding_generator: 嵌入生成器

    Returns:
        处理结果
    """
    processor = EnhancedFileProcessor()
    chunker = SmartTextChunker()

    # 保存文件
    filename = os.path.join(upload_folder, file.filename)
    file.save(filename)

    try:
        # 获取文件类型
        file_type = processor.get_file_type(file.filename)

        # 根据文件类型进行处理
        if file_type == 'document':
            if filename.lower().endswith('.pdf'):
                result = processor.process_pdf_enhanced(filename)
                text = result["text"]

                # 合并表格文本
                if result.get("table_text"):
                    text += "\n\n" + result["table_text"]

            else:  # .txt
                text = process_txt(filename)

        elif file_type == 'image':
            result = processor.process_image_file(filename)
            text = result["text"]

        elif file_type == 'table':
            result = processor.process_table_file(filename)
            text = result["text"]

        else:
            raise Exception(f"不支持的文件类型: {file_type}")

        # 智能分块文本
        chunks_with_metadata = chunker.chunk_text_smart(text)

        # 提取纯文本用于嵌入
        chunk_texts = [chunk["text"] for chunk in chunks_with_metadata]

        # 生成嵌入向量
        embeddings = embedding_generator(chunk_texts)

        # 存储向量（包含元数据）
        from milvus_manager import store_vectors_with_metadata
        try:
            ids = store_vectors_with_metadata(chunks_with_metadata, embeddings, collection)
        except:
            # 回退到原始存储方法
            from milvus_manager import store_vectors
            ids = store_vectors(chunk_texts, embeddings, collection)

        # 删除临时文件
        os.remove(filename)

        # 统计信息
        stats = {
            "total_chunks": len(chunks_with_metadata),
            "text_chunks": sum(1 for c in chunks_with_metadata if c["metadata"]["type"] == "text"),
            "table_chunks": sum(1 for c in chunks_with_metadata if c["metadata"]["has_table"]),
            "figure_chunks": sum(1 for c in chunks_with_metadata if c["metadata"]["has_figure"]),
            "file_type": file_type
        }

        return {
            "status": "success",
            "message": f"文件处理成功，已存储 {len(chunks_with_metadata)} 个智能片段",
            "chunks_count": len(chunks_with_metadata),
            "file_type": file_type,
            "stats": stats
        }

    except Exception as e:
        # 删除临时文件
        if os.path.exists(filename):
            os.remove(filename)
        raise e

# 保持向后兼容
def process_uploaded_file(file, upload_folder, collection, embedding_generator):
    """
    原始文件处理函数（向后兼容）
    """
    try:
        # 尝试使用增强处理
        return process_uploaded_file_enhanced(file, upload_folder, collection, embedding_generator)
    except Exception as e:
        logging.warning(f"增强处理失败，回退到基础处理: {str(e)}")

        # 回退到基础处理
        filename = os.path.join(upload_folder, file.filename)
        file.save(filename)

        # 处理文件
        if filename.lower().endswith('.pdf'):
            text = process_pdf(filename)
        else:  # .txt
            text = process_txt(filename)

        # 分块文本 (使用原算法)
        chunks = chunk_text(text, min_chunk_size=200, max_chunk_size=500, overlap=50)

        # 生成嵌入向量
        embeddings = embedding_generator(chunks)

        # 存储向量
        from milvus_manager import store_vectors
        ids = store_vectors(chunks, embeddings, collection)

        # 删除临时文件
        os.remove(filename)

        return {
            "status": "success",
            "message": f"文件处理成功，已存储 {len(chunks)} 个片段",
            "chunks_count": len(chunks)
        }

def process_pdf(file_path):
    """
    原始PDF处理函数（向后兼容）
    """
    try:
        doc = fitz.open(file_path)
        text = ""
        for page in doc:
            # 先尝试直接提取文本
            page_text = page.get_text("text")

            # 如果提取的文本过少（可能是图片PDF），尝试使用OCR
            if len(page_text) < 100:
                try:
                    pix = page.get_pixmap()
                    img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                    page_text = pytesseract.image_to_string(img, lang='chi_sim')
                except Exception as ocr_error:
                    logging.warning(f"OCR失败: {str(ocr_error)}")

            text += page_text

        # 清理文本
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    except Exception as e:
        raise Exception(f"处理PDF错误: {str(e)}")