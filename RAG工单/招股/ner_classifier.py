# -*- coding: utf-8 -*-
"""
命名实体识别和问题分类模块
实现对用户问题的实体识别、意图分类和智能路由
"""

import re
import logging
import jieba
import jieba.posseg as pseg
from typing import List, Dict, Tuple, Optional
from collections import defaultdict
import json
import os

# 配置日志
logger = logging.getLogger(__name__)

class NERClassifier:
    """
    命名实体识别和问题分类器
    提供实体识别、意图分类和智能提示词生成功能
    """
    
    def __init__(self):
        """
        初始化NER分类器
        加载实体词典和分类规则
        """
        try:
            # 初始化jieba分词
            jieba.initialize()
            
            # 加载自定义词典
            self._load_custom_dict()
            
            # 定义实体类型和模式
            self.entity_patterns = self._init_entity_patterns()
            
            # 定义问题分类规则
            self.question_categories = self._init_question_categories()
            
            # 定义提示词模板
            self.prompt_templates = self._init_prompt_templates()
            
            logger.info("NER分类器初始化完成")
            
        except Exception as e:
            logger.error(f"NER分类器初始化失败: {str(e)}")
            raise
    
    def _load_custom_dict(self):
        """
        加载自定义词典
        添加领域特定词汇
        """
        try:
            # 金融领域词汇
            financial_terms = [
                "招股说明书", "财务报表", "资产负债表", "利润表", "现金流量表",
                "营业收入", "净利润", "总资产", "净资产", "负债", "股东权益",
                "市盈率", "市净率", "资产负债率", "毛利率", "净利率",
                "IPO", "上市", "发行", "股票", "债券", "投资", "融资",
                "风险因素", "竞争优势", "商业模式", "行业分析", "市场前景"
            ]
            
            # 公司相关词汇
            company_terms = [
                "董事会", "监事会", "股东大会", "管理层", "高管",
                "子公司", "分公司", "关联方", "控股股东", "实际控制人",
                "经营范围", "主营业务", "业务模式", "产品服务", "客户"
            ]
            
            # 添加到jieba词典
            for term in financial_terms + company_terms:
                jieba.add_word(term)
            
            logger.info("自定义词典加载完成")
            
        except Exception as e:
            logger.warning(f"自定义词典加载失败: {str(e)}")
    
    def _init_entity_patterns(self) -> Dict[str, List[str]]:
        """
        初始化实体识别模式
        
        Returns:
            实体类型和对应的正则表达式模式
        """
        return {
            # 金额实体
            "MONEY": [
                r"(\d+(?:\.\d+)?)\s*(?:万|千万|亿|元|美元|港元)",
                r"(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*(?:元|美元|港元)",
                r"(?:人民币|美元|港元)\s*(\d+(?:\.\d+)?)"
            ],
            
            # 时间实体
            "TIME": [
                r"(\d{4})\s*年",
                r"(\d{4}年\d{1,2}月)",
                r"(\d{4}年\d{1,2}月\d{1,2}日)",
                r"(第[一二三四]季度)",
                r"(上半年|下半年|全年)"
            ],
            
            # 百分比实体
            "PERCENTAGE": [
                r"(\d+(?:\.\d+)?)\s*%",
                r"(\d+(?:\.\d+)?)\s*(?:个|百分点)"
            ],
            
            # 公司实体
            "COMPANY": [
                r"([A-Z][a-zA-Z0-9]*(?:\s+[A-Z][a-zA-Z0-9]*)*(?:公司|集团|有限公司|股份有限公司))",
                r"([\u4e00-\u9fa5]+(?:公司|集团|有限公司|股份有限公司))",
                r"([A-Z]{2,10}(?:\s+[A-Z]{2,10})*)"  # 股票代码
            ],
            
            # 财务指标实体
            "FINANCIAL_METRIC": [
                r"(营业收入|净利润|总资产|净资产|负债|股东权益)",
                r"(市盈率|市净率|资产负债率|毛利率|净利率)",
                r"(每股收益|每股净资产|净资产收益率)"
            ],
            
            # 业务实体
            "BUSINESS": [
                r"(主营业务|经营范围|业务模式|产品服务)",
                r"(市场份额|竞争优势|行业地位)",
                r"(研发投入|技术创新|专利)"
            ]
        }
    
    def _init_question_categories(self) -> Dict[str, Dict]:
        """
        初始化问题分类规则
        
        Returns:
            问题类别和对应的关键词、权重
        """
        return {
            "财务查询": {
                "keywords": ["收入", "利润", "资产", "负债", "财务", "业绩", "盈利", "亏损"],
                "weight": 1.0,
                "description": "查询财务数据和业绩信息"
            },
            
            "风险分析": {
                "keywords": ["风险", "挑战", "威胁", "不确定性", "问题", "困难"],
                "weight": 1.0,
                "description": "分析风险因素和挑战"
            },
            
            "业务咨询": {
                "keywords": ["业务", "产品", "服务", "市场", "客户", "竞争", "模式"],
                "weight": 1.0,
                "description": "咨询业务模式和市场情况"
            },
            
            "投资分析": {
                "keywords": ["投资", "估值", "前景", "价值", "回报", "股价", "市值"],
                "weight": 1.0,
                "description": "投资价值和前景分析"
            },
            
            "公司治理": {
                "keywords": ["治理", "管理", "董事", "股东", "控制", "决策", "制度"],
                "weight": 1.0,
                "description": "公司治理结构和管理"
            },
            
            "行业分析": {
                "keywords": ["行业", "市场", "趋势", "发展", "前景", "环境", "政策"],
                "weight": 1.0,
                "description": "行业发展和市场环境"
            },
            
            "技术创新": {
                "keywords": ["技术", "研发", "创新", "专利", "科技", "数字化", "智能"],
                "weight": 1.0,
                "description": "技术创新和研发能力"
            },
            
            "一般咨询": {
                "keywords": ["什么", "如何", "为什么", "介绍", "说明", "解释"],
                "weight": 0.5,
                "description": "一般性咨询和说明"
            }
        }
    
    def _init_prompt_templates(self) -> Dict[str, str]:
        """
        初始化提示词模板
        
        Returns:
            不同类别对应的提示词模板
        """
        return {
            "财务查询": """你是一位专业的财务分析师，请基于提供的财务文档回答用户关于财务数据的问题。
请重点关注：
1. 具体的财务数字和指标
2. 财务数据的变化趋势
3. 财务表现的原因分析
4. 与同行业的比较

用户问题：{query}
相关文档：{context}

请提供准确、专业的财务分析回答。""",

            "风险分析": """你是一位风险管理专家，请基于提供的文档分析相关的风险因素。
请重点关注：
1. 识别主要风险点
2. 评估风险影响程度
3. 分析风险应对措施
4. 提供风险管理建议

用户问题：{query}
相关文档：{context}

请提供全面、客观的风险分析。""",

            "业务咨询": """你是一位业务分析专家，请基于提供的文档回答关于业务模式和市场的问题。
请重点关注：
1. 业务模式和运营方式
2. 产品服务特点
3. 市场地位和竞争优势
4. 客户群体和市场策略

用户问题：{query}
相关文档：{context}

请提供详细、实用的业务分析。""",

            "投资分析": """你是一位投资分析师，请基于提供的文档进行投资价值分析。
请重点关注：
1. 投资价值和成长潜力
2. 估值水平和合理性
3. 投资风险和机会
4. 投资建议和策略

用户问题：{query}
相关文档：{context}

请提供专业、客观的投资分析。""",

            "公司治理": """你是一位公司治理专家，请基于提供的文档分析公司治理相关问题。
请重点关注：
1. 治理结构和制度
2. 管理层和决策机制
3. 股东权益保护
4. 内控和合规情况

用户问题：{query}
相关文档：{context}

请提供专业的治理分析。""",

            "行业分析": """你是一位行业分析师，请基于提供的文档进行行业和市场分析。
请重点关注：
1. 行业发展趋势
2. 市场环境变化
3. 政策影响分析
4. 竞争格局评估

用户问题：{query}
相关文档：{context}

请提供深入的行业分析。""",

            "技术创新": """你是一位技术专家，请基于提供的文档分析技术创新相关问题。
请重点关注：
1. 技术实力和创新能力
2. 研发投入和成果
3. 技术竞争优势
4. 数字化转型进展

用户问题：{query}
相关文档：{context}

请提供专业的技术分析。""",

            "一般咨询": """你是一位专业的AI助手，请基于提供的文档回答用户的问题。
请确保回答：
1. 准确性和客观性
2. 逻辑清晰和条理分明
3. 内容全面和有针对性
4. 语言专业和易懂

用户问题：{query}
相关文档：{context}

请提供准确、有用的回答。"""
        }
    
    def extract_entities(self, text: str) -> Dict[str, List[Dict]]:
        """
        提取文本中的命名实体
        
        Args:
            text: 输入文本
            
        Returns:
            实体类型和实体列表的字典
        """
        entities = defaultdict(list)
        
        try:
            # 使用正则表达式提取实体
            for entity_type, patterns in self.entity_patterns.items():
                for pattern in patterns:
                    matches = re.finditer(pattern, text, re.IGNORECASE)
                    for match in matches:
                        entity_text = match.group(1) if match.groups() else match.group(0)
                        entities[entity_type].append({
                            "text": entity_text,
                            "start": match.start(),
                            "end": match.end(),
                            "confidence": 0.9  # 正则匹配的置信度
                        })
            
            # 使用jieba进行词性标注，提取人名、地名等
            words = pseg.cut(text)
            for word, flag in words:
                if flag == 'nr':  # 人名
                    entities["PERSON"].append({
                        "text": word,
                        "start": text.find(word),
                        "end": text.find(word) + len(word),
                        "confidence": 0.8
                    })
                elif flag == 'ns':  # 地名
                    entities["LOCATION"].append({
                        "text": word,
                        "start": text.find(word),
                        "end": text.find(word) + len(word),
                        "confidence": 0.8
                    })
                elif flag == 'nt':  # 机构名
                    entities["ORGANIZATION"].append({
                        "text": word,
                        "start": text.find(word),
                        "end": text.find(word) + len(word),
                        "confidence": 0.8
                    })
            
            # 去重和排序
            for entity_type in entities:
                entities[entity_type] = self._deduplicate_entities(entities[entity_type])
            
            logger.info(f"实体提取完成，共提取{sum(len(v) for v in entities.values())}个实体")
            return dict(entities)
            
        except Exception as e:
            logger.error(f"实体提取失败: {str(e)}")
            return {}
    
    def _deduplicate_entities(self, entities: List[Dict]) -> List[Dict]:
        """
        去除重复实体
        
        Args:
            entities: 实体列表
            
        Returns:
            去重后的实体列表
        """
        seen = set()
        unique_entities = []
        
        for entity in entities:
            entity_key = (entity["text"].lower(), entity["start"], entity["end"])
            if entity_key not in seen:
                seen.add(entity_key)
                unique_entities.append(entity)
        
        # 按置信度排序
        return sorted(unique_entities, key=lambda x: x["confidence"], reverse=True)
    
    def classify_question(self, question: str) -> Dict[str, any]:
        """
        对问题进行分类
        
        Args:
            question: 用户问题
            
        Returns:
            分类结果，包含类别、置信度等信息
        """
        try:
            # 分词
            words = [word for word, _ in pseg.cut(question.lower())]
            
            # 计算每个类别的得分
            category_scores = {}
            for category, config in self.question_categories.items():
                score = 0
                matched_keywords = []
                
                for keyword in config["keywords"]:
                    if keyword in question.lower():
                        score += config["weight"]
                        matched_keywords.append(keyword)
                
                if score > 0:
                    category_scores[category] = {
                        "score": score,
                        "matched_keywords": matched_keywords,
                        "description": config["description"]
                    }
            
            # 选择得分最高的类别
            if category_scores:
                best_category = max(category_scores.keys(), 
                                  key=lambda x: category_scores[x]["score"])
                
                result = {
                    "category": best_category,
                    "confidence": min(category_scores[best_category]["score"] / 3.0, 1.0),
                    "matched_keywords": category_scores[best_category]["matched_keywords"],
                    "description": category_scores[best_category]["description"],
                    "all_scores": category_scores
                }
            else:
                # 默认分类
                result = {
                    "category": "一般咨询",
                    "confidence": 0.5,
                    "matched_keywords": [],
                    "description": "一般性咨询和说明",
                    "all_scores": {}
                }
            
            logger.info(f"问题分类完成: {result['category']} (置信度: {result['confidence']:.2f})")
            return result
            
        except Exception as e:
            logger.error(f"问题分类失败: {str(e)}")
            return {
                "category": "一般咨询",
                "confidence": 0.0,
                "matched_keywords": [],
                "description": "分类失败，使用默认类别",
                "all_scores": {}
            }
    
    def generate_enhanced_prompt(self, query: str, context: List[Dict], 
                               classification: Dict) -> str:
        """
        根据分类结果生成增强的提示词
        
        Args:
            query: 用户问题
            context: 上下文文档
            classification: 问题分类结果
            
        Returns:
            增强的提示词
        """
        try:
            category = classification.get("category", "一般咨询")
            template = self.prompt_templates.get(category, self.prompt_templates["一般咨询"])
            
            # 构建上下文文本
            context_text = "\n\n".join([doc.get("text", "") for doc in context])
            
            # 生成提示词
            enhanced_prompt = template.format(
                query=query,
                context=context_text
            )
            
            logger.info(f"生成增强提示词，类别: {category}")
            return enhanced_prompt
            
        except Exception as e:
            logger.error(f"生成增强提示词失败: {str(e)}")
            # 返回基础提示词
            return f"请基于以下文档回答问题：\n\n问题：{query}\n\n文档：{context_text}"
    
    def analyze_query(self, query: str) -> Dict[str, any]:
        """
        综合分析用户查询
        
        Args:
            query: 用户查询
            
        Returns:
            分析结果，包含实体、分类等信息
        """
        try:
            # 提取实体
            entities = self.extract_entities(query)
            
            # 问题分类
            classification = self.classify_question(query)
            
            # 生成分析报告
            analysis = {
                "query": query,
                "entities": entities,
                "classification": classification,
                "entity_count": sum(len(v) for v in entities.values()),
                "has_financial_entities": bool(entities.get("MONEY") or entities.get("FINANCIAL_METRIC")),
                "has_time_entities": bool(entities.get("TIME")),
                "complexity": self._assess_query_complexity(query, entities, classification)
            }
            
            logger.info(f"查询分析完成: {analysis['classification']['category']}, "
                       f"实体数: {analysis['entity_count']}, "
                       f"复杂度: {analysis['complexity']}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"查询分析失败: {str(e)}")
            return {
                "query": query,
                "entities": {},
                "classification": {"category": "一般咨询", "confidence": 0.0},
                "entity_count": 0,
                "has_financial_entities": False,
                "has_time_entities": False,
                "complexity": "simple"
            }
    
    def _assess_query_complexity(self, query: str, entities: Dict, 
                               classification: Dict) -> str:
        """
        评估查询复杂度
        
        Args:
            query: 查询文本
            entities: 提取的实体
            classification: 分类结果
            
        Returns:
            复杂度等级: simple, medium, complex
        """
        try:
            complexity_score = 0
            
            # 基于查询长度
            if len(query) > 50:
                complexity_score += 1
            if len(query) > 100:
                complexity_score += 1
            
            # 基于实体数量
            entity_count = sum(len(v) for v in entities.values())
            if entity_count > 2:
                complexity_score += 1
            if entity_count > 5:
                complexity_score += 1
            
            # 基于分类置信度
            if classification.get("confidence", 0) > 0.8:
                complexity_score += 1
            
            # 基于关键词匹配数量
            matched_keywords = len(classification.get("matched_keywords", []))
            if matched_keywords > 2:
                complexity_score += 1
            
            # 判断复杂度等级
            if complexity_score <= 2:
                return "simple"
            elif complexity_score <= 4:
                return "medium"
            else:
                return "complex"
                
        except Exception as e:
            logger.error(f"复杂度评估失败: {str(e)}")
            return "simple"

# 全局NER分类器实例
ner_classifier = None

def init_ner_classifier():
    """
    初始化全局NER分类器
    
    Returns:
        NERClassifier实例
    """
    global ner_classifier
    try:
        if ner_classifier is None:
            ner_classifier = NERClassifier()
        return ner_classifier
    except Exception as e:
        logger.error(f"NER分类器初始化失败: {str(e)}")
        return None

def get_ner_classifier():
    """
    获取NER分类器实例
    
    Returns:
        NERClassifier实例或None
    """
    global ner_classifier
    if ner_classifier is None:
        return init_ner_classifier()
    return ner_classifier
