# -*- coding: utf-8 -*-
import redis
import json
import logging
from datetime import datetime

# Redis 连接配置
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_DB = 0
SESSION_EXPIRE = 3600  # 1小时过期时间
MAX_HISTORY_LENGTH = 50  # 最大对话历史长度

class RedisManager:
    def __init__(self):
        try:
            self.connection = redis.Redis(
                host=REDIS_HOST,
                port=REDIS_PORT,
                db=REDIS_DB,
                decode_responses=True
            )
            self.connection.ping()
            logging.info("Redis 连接成功")
        except Exception as e:
            logging.error(f"Redis 连接失败: {str(e)}")
            raise

    def store_session(self, session_id, history):
        """存储会话历史，限制历史长度"""
        try:
            # 限制历史长度，保留最近的对话
            if len(history) > MAX_HISTORY_LENGTH:
                history = history[-MAX_HISTORY_LENGTH:]

            # 添加时间戳
            session_data = {
                'history': history,
                'last_updated': datetime.now().isoformat(),
                'message_count': len(history)
            }

            self.connection.setex(
                f"session:{session_id}",
                SESSION_EXPIRE,
                json.dumps(session_data, ensure_ascii=False)
            )
            logging.info(f"会话已保存: {session_id}, 消息数: {len(history)}")
        except Exception as e:
            logging.error(f"保存会话失败: {str(e)}")

    def get_session(self, session_id):
        """获取会话历史"""
        try:
            session_data = self.connection.get(f"session:{session_id}")
            if session_data:
                data = json.loads(session_data)
                # 兼容旧格式
                if isinstance(data, list):
                    return data
                return data.get('history', [])
            return []
        except Exception as e:
            logging.error(f"获取会话失败: {str(e)}")
            return []

    def get_session_info(self, session_id):
        """获取会话详细信息"""
        try:
            session_data = self.connection.get(f"session:{session_id}")
            if session_data:
                data = json.loads(session_data)
                if isinstance(data, dict):
                    return data
                # 兼容旧格式
                return {
                    'history': data,
                    'last_updated': None,
                    'message_count': len(data)
                }
            return None
        except Exception as e:
            logging.error(f"获取会话信息失败: {str(e)}")
            return None

    def clear_session(self, session_id):
        """清除会话历史"""
        try:
            result = self.connection.delete(f"session:{session_id}")
            if result:
                logging.info(f"会话已清除: {session_id}")
                return True
            return False
        except Exception as e:
            logging.error(f"清除会话失败: {str(e)}")
            return False

    def extend_session(self, session_id):
        """延长会话过期时间"""
        try:
            self.connection.expire(f"session:{session_id}", SESSION_EXPIRE)
            return True
        except Exception as e:
            logging.error(f"延长会话失败: {str(e)}")
            return False

    def get_all_sessions(self):
        """获取所有活跃会话（用于管理）"""
        try:
            keys = self.connection.keys("session:*")
            sessions = []
            for key in keys:
                session_id = key.replace("session:", "")
                info = self.get_session_info(session_id)
                if info:
                    sessions.append({
                        'session_id': session_id,
                        'message_count': info.get('message_count', 0),
                        'last_updated': info.get('last_updated'),
                        'ttl': self.connection.ttl(key)
                    })
            return sessions
        except Exception as e:
            logging.error(f"获取会话列表失败: {str(e)}")
            return []

# 全局 Redis 管理器
redis_manager = RedisManager()