#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF图像解析功能测试脚本 - 简化版
"""

import os
import sys
import logging
from file_processor import process_pdf

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_pdf_image_parsing():
    """测试PDF图像解析功能"""
    print("🔍 测试PDF图像解析功能...")
    
    # 检查uploads目录
    uploads_dir = "uploads"
    if not os.path.exists(uploads_dir):
        os.makedirs(uploads_dir)
        print(f"✅ 创建uploads目录: {uploads_dir}")
    
    # 查找PDF文件
    pdf_files = []
    for file in os.listdir(uploads_dir):
        if file.lower().endswith('.pdf'):
            pdf_files.append(os.path.join(uploads_dir, file))
    
    if not pdf_files:
        print("❌ 在uploads目录中未找到PDF文件")
        print("请将包含图像的PDF文件放入uploads目录中进行测试")
        return False
    
    print(f"📁 找到{len(pdf_files)}个PDF文件:")
    for pdf_file in pdf_files:
        print(f"   - {os.path.basename(pdf_file)}")
    
    # 测试每个PDF文件
    success_count = 0
    for pdf_file in pdf_files:
        print(f"\n📄 测试文件: {os.path.basename(pdf_file)}")
        try:
            # 处理PDF
            text_content = process_pdf(pdf_file)
            
            if text_content:
                print(f"✅ 成功提取内容，长度: {len(text_content)} 字符")
                
                # 检查是否包含图像信息
                if "图像" in text_content and "文字内容:" in text_content:
                    image_count = text_content.count("文字内容:")
                    print(f"🖼️  检测到 {image_count} 个包含文字的图像")
                    
                    # 显示部分图像信息
                    lines = text_content.split('\n')
                    for line in lines:
                        if "图像" in line and "文字内容:" in line:
                            print(f"   {line[:100]}...")
                            break
                else:
                    print("ℹ️  未检测到图像或图像中无文字内容")
                
                # 显示文本内容预览
                preview = text_content[:200].replace('\n', ' ')
                print(f"📝 内容预览: {preview}...")
                
                success_count += 1
                
            else:
                print("❌ 未能提取到内容")
                
        except Exception as e:
            print(f"❌ 处理失败: {str(e)}")
    
    print(f"\n📊 测试结果: {success_count}/{len(pdf_files)} 个文件处理成功")
    return success_count > 0

def test_dependencies():
    """测试依赖项"""
    print("🔍 检查依赖项...")
    
    dependencies = [
        ('fitz', 'PyMuPDF'),
        ('PIL', 'Pillow'),
        ('pytesseract', 'pytesseract'),
        ('numpy', 'numpy')
    ]
    
    missing_deps = []
    
    for module_name, package_name in dependencies:
        try:
            __import__(module_name)
            print(f"✅ {package_name} 已安装")
        except ImportError:
            print(f"❌ {package_name} 未安装")
            missing_deps.append(package_name)
    
    # 检查tesseract可执行文件
    try:
        import pytesseract
        pytesseract.get_tesseract_version()
        print("✅ Tesseract OCR 引擎可用")
    except Exception as e:
        print(f"❌ Tesseract OCR 引擎不可用: {str(e)}")
        print("请安装Tesseract OCR:")
        print("  Windows: 下载安装包 https://github.com/UB-Mannheim/tesseract/wiki")
        print("  Linux: sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim")
        print("  Mac: brew install tesseract tesseract-lang")
        missing_deps.append("tesseract-ocr")
    
    if missing_deps:
        print(f"\n⚠️  缺少依赖项: {', '.join(missing_deps)}")
        return False
    
    print("✅ 所有依赖项检查通过")
    return True

def main():
    """主函数"""
    print("🚀 PDF图像解析功能测试 - 简化版")
    print("=" * 50)
    
    # 检查依赖项
    if not test_dependencies():
        print("\n❌ 依赖项检查失败，请先安装必要的依赖")
        return
    
    # 测试PDF图像解析
    success = test_pdf_image_parsing()
    
    if success:
        print("\n🎉 PDF图像解析功能测试完成！")
        print("✅ 系统可以成功提取PDF中的图像内容")
    else:
        print("\n⚠️  测试未完全成功")
        print("💡 请确保PDF文件包含图像内容")

if __name__ == "__main__":
    main()
