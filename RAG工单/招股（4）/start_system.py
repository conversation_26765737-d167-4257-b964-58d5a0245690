#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多轮对话系统启动脚本
自动检查依赖并启动系统
"""

import os
import sys
import subprocess
import time
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """检查Python版本"""
    logger.info("检查Python版本...")
    if sys.version_info < (3, 8):
        logger.error("需要Python 3.8或更高版本")
        return False
    logger.info(f"Python版本: {sys.version}")
    return True

def check_redis_service():
    """检查Redis服务"""
    logger.info("检查Redis服务...")
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        logger.info("✅ Redis服务正常")
        return True
    except Exception as e:
        logger.error(f"❌ Redis服务异常: {str(e)}")
        logger.info("请确保Redis服务正在运行:")
        logger.info("  Windows: 下载并启动Redis服务")
        logger.info("  Linux/Mac: sudo systemctl start redis 或 brew services start redis")
        return False

def install_dependencies():
    """安装依赖"""
    logger.info("检查并安装依赖...")
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        logger.error("requirements.txt文件不存在")
        return False
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        logger.info("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ 依赖安装失败: {str(e)}")
        return False

def check_milvus_service():
    """检查Milvus服务"""
    logger.info("检查Milvus服务...")
    try:
        from pymilvus import connections
        connections.connect("default", host="localhost", port="19530")
        logger.info("✅ Milvus服务正常")
        return True
    except Exception as e:
        logger.error(f"❌ Milvus服务异常: {str(e)}")
        logger.info("请确保Milvus服务正在运行:")
        logger.info("  Docker: docker run -p 19530:19530 milvusdb/milvus:latest")
        return False

def create_directories():
    """创建必要的目录"""
    logger.info("创建必要的目录...")
    directories = ["uploads", "logs"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logger.info(f"✅ 目录创建: {directory}")

def start_application():
    """启动应用"""
    logger.info("启动多轮对话系统...")
    try:
        # 设置环境变量
        os.environ['FLASK_APP'] = 'app.py'
        os.environ['FLASK_ENV'] = 'development'
        
        # 启动Flask应用
        subprocess.run([sys.executable, "app.py"])
        
    except KeyboardInterrupt:
        logger.info("应用已停止")
    except Exception as e:
        logger.error(f"启动失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 多轮对话系统启动器")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    # 安装依赖
    if not install_dependencies():
        logger.warning("依赖安装失败，尝试继续运行...")
    
    # 检查服务
    redis_ok = check_redis_service()
    milvus_ok = check_milvus_service()
    
    if not redis_ok:
        logger.error("Redis服务未运行，多轮对话功能将不可用")
        response = input("是否继续启动？(y/N): ")
        if response.lower() != 'y':
            sys.exit(1)
    
    if not milvus_ok:
        logger.error("Milvus服务未运行，向量搜索功能将不可用")
        response = input("是否继续启动？(y/N): ")
        if response.lower() != 'y':
            sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 系统检查完成，启动应用...")
    print("访问地址: http://localhost:5000")
    print("按 Ctrl+C 停止应用")
    print("=" * 50)
    
    # 启动应用
    start_application()

if __name__ == "__main__":
    main()
