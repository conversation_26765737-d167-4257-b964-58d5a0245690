#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多轮对话系统测试脚本
测试Redis聊天记录保存和前端界面功能
"""

import requests
import json
import time
from redis_manager import redis_manager

def test_redis_connection():
    """测试Redis连接"""
    print("🔍 测试Redis连接...")
    try:
        # 测试基本连接
        redis_manager.connection.ping()
        print("✅ Redis连接成功")
        
        # 测试存储和获取
        test_session_id = "test_session_123"
        test_history = [
            {"role": "user", "content": "测试消息1", "timestamp": "12345"},
            {"role": "assistant", "content": "测试回复1", "timestamp": "12346"}
        ]
        
        redis_manager.store_session(test_session_id, test_history)
        retrieved_history = redis_manager.get_session(test_session_id)
        
        if retrieved_history == test_history:
            print("✅ Redis存储和获取功能正常")
        else:
            print("❌ Redis存储和获取功能异常")
            
        # 清理测试数据
        redis_manager.clear_session(test_session_id)
        print("✅ Redis测试完成")
        
    except Exception as e:
        print(f"❌ Redis测试失败: {str(e)}")
        return False
    
    return True

def test_api_endpoints():
    """测试API端点"""
    print("\n🔍 测试API端点...")
    base_url = "http://localhost:5000"
    
    try:
        # 测试会话信息获取
        response = requests.get(f"{base_url}/session/info")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 会话信息API正常: {data.get('session_id', 'N/A')}")
        else:
            print(f"❌ 会话信息API异常: {response.status_code}")
            
        # 测试聊天历史获取
        response = requests.get(f"{base_url}/chat/history")
        if response.status_code == 200:
            print("✅ 聊天历史API正常")
        else:
            print(f"❌ 聊天历史API异常: {response.status_code}")
            
        # 测试清除聊天历史
        response = requests.post(f"{base_url}/chat/clear")
        if response.status_code == 200:
            print("✅ 清除聊天API正常")
        else:
            print(f"❌ 清除聊天API异常: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保应用正在运行")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False
    
    return True

def test_chat_flow():
    """测试完整的对话流程"""
    print("\n🔍 测试完整对话流程...")
    base_url = "http://localhost:5000"
    
    try:
        # 发送测试查询
        test_query = "你好，这是一个测试问题"
        response = requests.post(
            f"{base_url}/query",
            json={"query": test_query},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                print("✅ 查询API正常工作")
                print(f"   会话ID: {data.get('session_id', 'N/A')}")
                print(f"   回复长度: {len(data.get('answer', ''))}")
                print(f"   聊天历史长度: {len(data.get('chat_history', []))}")
            else:
                print(f"❌ 查询失败: {data.get('message', 'Unknown error')}")
        else:
            print(f"❌ 查询API异常: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 对话流程测试失败: {str(e)}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始测试多轮对话系统...")
    print("=" * 50)
    
    # 测试Redis连接
    redis_ok = test_redis_connection()
    
    # 测试API端点（需要服务器运行）
    api_ok = test_api_endpoints()
    
    # 测试对话流程
    chat_ok = test_chat_flow()
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print(f"   Redis功能: {'✅ 正常' if redis_ok else '❌ 异常'}")
    print(f"   API端点: {'✅ 正常' if api_ok else '❌ 异常'}")
    print(f"   对话流程: {'✅ 正常' if chat_ok else '❌ 异常'}")
    
    if redis_ok and api_ok and chat_ok:
        print("\n🎉 所有测试通过！多轮对话系统工作正常。")
    else:
        print("\n⚠️  部分测试失败，请检查相关组件。")
        if not redis_ok:
            print("   - 请确保Redis服务正在运行")
        if not api_ok or not chat_ok:
            print("   - 请确保Flask应用正在运行 (python app.py)")

if __name__ == "__main__":
    main()
