from sentence_transformers import SentenceTransformer
import logging
import numpy as np
import time
from typing import List, Union, Optional
import torch
import gc

# 全局嵌入模型
embedding_model = None

# 模型配置
MODEL_CONFIG = {
    "device": "cuda" if torch.cuda.is_available() else "cpu",
    "batch_size": 32,  # 批处理大小
    "max_seq_length": 512,  # 最大序列长度
    "normalize_embeddings": True,  # 是否标准化嵌入向量
    "show_progress_bar": False,  # 关闭进度条以提高性能
}


def init_embedding_model(model_path: str = r"D:\model\BAAI\bge-m3") -> SentenceTransformer:
    """
    初始化优化的嵌入模型

    Args:
        model_path: 模型路径

    Returns:
        SentenceTransformer: 初始化的模型
    """
    global embedding_model
    try:
        start_time = time.time()

        # 加载模型并应用优化配置
        embedding_model = SentenceTransformer(
            model_path,
            device=MODEL_CONFIG["device"]
        )

        # 设置模型配置
        embedding_model.max_seq_length = MODEL_CONFIG["max_seq_length"]

        # 如果使用GPU，优化内存使用
        if MODEL_CONFIG["device"] == "cuda":
            torch.cuda.empty_cache()  # 清空GPU缓存

        # 预热模型（提高首次推理速度）
        _warmup_model(embedding_model)

        init_time = time.time() - start_time
        logging.info(f"嵌入模型初始化完成，设备: {MODEL_CONFIG['device']}, 耗时: {init_time:.2f}秒")

        return embedding_model

    except Exception as e:
        logging.error(f"嵌入模型初始化失败: {str(e)}")
        raise


def _warmup_model(model: SentenceTransformer):
    """
    预热模型以提高首次推理速度

    Args:
        model: SentenceTransformer模型
    """
    try:
        # 使用简短文本进行预热
        warmup_texts = ["预热文本", "warmup text"]
        model.encode(warmup_texts, show_progress_bar=False)
        logging.info("模型预热完成")

    except Exception as e:
        logging.warning(f"模型预热失败: {str(e)}")


def generate_embeddings(texts: Union[str, List[str]],
                       batch_size: Optional[int] = None,
                       normalize: bool = True) -> np.ndarray:
    """
    优化的嵌入向量生成函数

    Args:
        texts: 输入文本或文本列表
        batch_size: 批处理大小，None使用默认值
        normalize: 是否标准化向量

    Returns:
        np.ndarray: 嵌入向量数组
    """
    global embedding_model

    if embedding_model is None:
        init_embedding_model()

    try:
        start_time = time.time()

        # 处理输入格式
        if isinstance(texts, str):
            texts = [texts]

        if not texts:
            return np.array([])

        # 使用指定的批处理大小
        effective_batch_size = batch_size or MODEL_CONFIG["batch_size"]

        # 文本预处理
        processed_texts = _preprocess_texts(texts)

        # 生成嵌入向量
        embeddings = embedding_model.encode(
            processed_texts,
            batch_size=effective_batch_size,
            normalize_embeddings=normalize,
            show_progress_bar=MODEL_CONFIG["show_progress_bar"],
            convert_to_numpy=True,
            device=MODEL_CONFIG["device"]
        )

        # 确保返回float32格式以节省内存
        if embeddings.dtype != np.float32:
            embeddings = embeddings.astype(np.float32)

        # 清理GPU内存（如果使用GPU）
        if MODEL_CONFIG["device"] == "cuda":
            torch.cuda.empty_cache()

        generation_time = time.time() - start_time
        logging.info(f"生成嵌入向量: {len(texts)} 个文本，耗时: {generation_time:.3f}秒")

        return embeddings

    except Exception as e:
        logging.error(f"生成嵌入向量失败: {str(e)}")
        raise


def _preprocess_texts(texts: List[str]) -> List[str]:
    """
    文本预处理，优化嵌入质量

    Args:
        texts: 原始文本列表

    Returns:
        List[str]: 预处理后的文本列表
    """
    processed_texts = []

    for text in texts:
        if not text or not isinstance(text, str):
            processed_texts.append("")
            continue

        # 基本清理
        cleaned_text = text.strip()

        # 限制文本长度（避免超出模型最大长度）
        max_chars = MODEL_CONFIG["max_seq_length"] * 4  # 粗略估算字符数
        if len(cleaned_text) > max_chars:
            cleaned_text = cleaned_text[:max_chars] + "..."

        processed_texts.append(cleaned_text)

    return processed_texts


def generate_embeddings_batch(text_batches: List[List[str]],
                             batch_size: Optional[int] = None) -> List[np.ndarray]:
    """
    批量生成嵌入向量，适用于大量文本处理

    Args:
        text_batches: 文本批次列表
        batch_size: 每批处理大小

    Returns:
        List[np.ndarray]: 每批的嵌入向量数组列表
    """
    try:
        start_time = time.time()
        all_embeddings = []

        for i, batch_texts in enumerate(text_batches):
            if not batch_texts:
                all_embeddings.append(np.array([]))
                continue

            batch_start_time = time.time()
            batch_embeddings = generate_embeddings(
                batch_texts,
                batch_size=batch_size,
                normalize=True
            )
            batch_time = time.time() - batch_start_time

            all_embeddings.append(batch_embeddings)

            logging.info(f"批次 {i+1}/{len(text_batches)}: {len(batch_texts)} 个文本，"
                        f"耗时: {batch_time:.3f}秒")

        total_time = time.time() - start_time
        total_texts = sum(len(batch) for batch in text_batches)

        logging.info(f"批量嵌入生成完成: {total_texts} 个文本，总耗时: {total_time:.3f}秒")

        return all_embeddings

    except Exception as e:
        logging.error(f"批量生成嵌入向量失败: {str(e)}")
        raise


def get_model_info() -> dict:
    """
    获取模型信息

    Returns:
        dict: 模型信息
    """
    global embedding_model

    if embedding_model is None:
        return {"status": "not_initialized"}

    try:
        return {
            "status": "initialized",
            "device": MODEL_CONFIG["device"],
            "max_seq_length": embedding_model.max_seq_length,
            "model_name": embedding_model._modules.get("0", {}).get("auto_model", {}).name_or_path if hasattr(embedding_model, "_modules") else "unknown",
            "batch_size": MODEL_CONFIG["batch_size"],
            "normalize_embeddings": MODEL_CONFIG["normalize_embeddings"]
        }

    except Exception as e:
        logging.error(f"获取模型信息失败: {str(e)}")
        return {"status": "error", "error": str(e)}


def optimize_memory():
    """
    优化内存使用
    """
    try:
        # Python垃圾回收
        gc.collect()

        # GPU内存清理
        if MODEL_CONFIG["device"] == "cuda" and torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()

        logging.info("内存优化完成")

    except Exception as e:
        logging.warning(f"内存优化失败: {str(e)}")