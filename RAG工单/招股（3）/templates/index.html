<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能文档问答系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        tertiary: '#8B5CF6',
                        neutral: '#64748B',
                        dark: '#1E293B',
                        light: '#F8FAFC',
                        card: '#F9FAFB',
                        accuracy: '#F59E0B' // 新增准确率颜色
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                    boxShadow: {
                        card: '0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03)',
                        hover: '0 10px 15px -3px rgba(0, 0, 0, 0.07)'
                    }
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .shadow-soft {
                box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
            }
            .transition-custom {
                transition: all 0.3s ease;
            }
            .prose-custom {
                line-height: 1.7;
                max-width: none;
            }
            .prose-custom h3 {
                font-size: 1.25rem;
                font-weight: 600;
                margin-top: 1.5rem;
                margin-bottom: 1rem;
                color: #1E293B;
            }
            .prose-custom p {
                margin-bottom: 1rem;
            }
            .prose-custom ul {
                list-style-type: disc;
                padding-left: 1.5rem;
                margin-bottom: 1rem;
            }
            .prose-custom ol {
                list-style-type: decimal;
                padding-left: 1.5rem;
                margin-bottom: 1rem;
            }
            .prose-custom li {
                margin-bottom: 0.5rem;
            }
            .prose-custom blockquote {
                border-left: 4px solid #3B82F6;
                padding-left: 1rem;
                margin: 1.5rem 0;
                color: #64748B;
                font-style: italic;
            }
            .prose-custom strong {
                font-weight: 600;
            }
            /* 新增准确率进度条样式 */
            .accuracy-bar {
                height: 6px;
                border-radius: 3px;
                background-color: #E5E7EB;
                overflow: hidden;
            }
            .accuracy-fill {
                height: 100%;
                background-color: #F59E0B;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen font-sans">
    <header class="bg-gradient-to-r from-primary to-tertiary shadow-lg sticky top-0 z-10">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <div class="bg-white p-2 rounded-full shadow-md">
                    <i class="fa fa-file-text-o text-primary text-xl"></i>
                </div>
                <h1 class="text-xl font-bold text-white">智能文档问答系统</h1>
            </div>
            <div class="text-xs text-white/80 bg-black/10 px-2 py-1 rounded">
                基于BGE-M3嵌入模型、BGE-reranker重排序和Milvus向量数据库
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8 max-w-6xl">
        <!-- 文件上传区域 -->
        <section class="bg-white rounded-xl shadow-card p-6 mb-8 transition-custom hover:shadow-hover">
            <h2 class="text-lg font-semibold mb-4 flex items-center">
                <i class="fa fa-cloud-upload text-primary mr-2"></i>
                上传文档
            </h2>
            <p class="text-neutral text-sm mb-4">支持上传PDF和TXT文件，系统将处理并存储文档内容以便后续查询</p>

            <div id="drop-area" class="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center transition-custom hover:border-primary bg-card">
                <i class="fa fa-file-text-o text-4xl text-neutral mb-3"></i>
                <p class="mb-2 text-neutral-700">拖放文件到此处，或</p>
                <label class="inline-block bg-primary text-white px-4 py-2 rounded-lg cursor-pointer hover:bg-primary/90 transition-custom shadow-md">
                    <i class="fa fa-folder-open mr-1"></i> 选择文件
                    <input type="file" id="file-input" class="hidden" accept=".pdf,.txt">
                </label>
                <p class="text-xs text-neutral/60 mt-3">支持 PDF, TXT 格式</p>
            </div>

            <div id="upload-status" class="mt-4 hidden">
                <div class="flex items-center">
                    <i id="status-icon" class="mr-2"></i>
                    <span id="status-message"></span>
                </div>
                <div id="progress-container" class="mt-2 h-2 bg-gray-200 rounded-full overflow-hidden hidden">
                    <div id="progress-bar" class="h-full bg-secondary w-0 transition-all duration-300"></div>
                </div>
            </div>

        </section>

        <!-- 查询区域 -->
        <section class="bg-white rounded-xl shadow-card p-6 mb-8 transition-custom hover:shadow-hover">
            <h2 class="text-lg font-semibold mb-4 flex items-center">
                <i class="fa fa-search text-secondary mr-2"></i>
                文档查询
            </h2>
            <div class="relative">
                <input type="text" id="query-input" placeholder="输入您的问题..." class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent transition-custom">
                <button id="search-button" class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary text-white px-4 py-1.5 rounded-md hover:bg-primary/90 transition-custom">
                    <i class="fa fa-paper-plane mr-1"></i> 查询
                </button>
            </div>

            <!-- 新增：查询处理状态 -->
            <div id="query-status" class="mt-4 hidden flex items-center">
                <i class="fa fa-spinner fa-spin text-primary mr-2"></i>
                <span>正在处理您的查询...</span>
            </div>

            <!-- 答案展示区域 -->
            <div id="answer-container" class="mt-8 hidden">
                <h3 class="text-lg font-semibold mb-3">答案：</h3>
                <div class="bg-card rounded-xl p-4 prose-custom" id="answer-content"></div>
                <div class="mt-3 text-sm text-neutral flex justify-end items-center">
                    <span>推理模型:</span>
                    <span id="model-name" class="ml-1 px-2 py-1 bg-secondary/20 text-secondary rounded-md">未指定</span>
                </div>
            </div>

            <!-- 新增：参考文档区域（带准确率显示） -->
            <div id="reference-container" class="mt-6 hidden">
                <h3 class="text-lg font-semibold mb-3 flex items-center">
                    <i class="fa fa-file-text-o text-tertiary mr-2"></i>
                    参考文档（按相关性排序）
                </h3>
                <div class="space-y-3" id="reference-list">
                    <!-- 动态添加参考文档 -->
                </div>
            </div>
        </section>

        <!-- 新增：系统信息卡片 -->
        <section class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div class="bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl p-4 border border-primary/20">
                <div class="flex items-center">
                    <div class="bg-primary/10 p-2 rounded-lg mr-3">
                        <i class="fa fa-microchip text-primary text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-primary">嵌入模型</h3>
                        <p class="text-sm">BGE-M3 · 本地推理</p>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-br from-secondary/10 to-secondary/5 rounded-xl p-4 border border-secondary/20">
                <div class="flex items-center">
                    <div class="bg-secondary/10 p-2 rounded-lg mr-3">
                        <i class="fa fa-database text-secondary text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-secondary">向量数据库</h3>
                        <p class="text-sm">Milvus · 1024维</p>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-br from-tertiary/10 to-tertiary/5 rounded-xl p-4 border border-tertiary/20">
                <div class="flex items-center">
                    <div class="bg-tertiary/10 p-2 rounded-lg mr-3">
                        <i class="fa fa-sort-amount-desc text-tertiary text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-tertiary">重排序模型</h3>
                        <p class="text-sm">BGE-reranker-v2-m3</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="bg-dark text-white py-6">
        <div class="container mx-auto px-4 text-center text-sm">
            <p>智能文档问答系统 &copy; 2025 - 基于本地化AI技术构建</p>
            <p class="mt-2 text-white/70">本系统使用本地AI模型处理文档，确保数据安全</p>
        </div>
    </footer>

    <script>
        // 文件上传处理
        const dropArea = document.getElementById('drop-area');
        const fileInput = document.getElementById('file-input');
        const uploadStatus = document.getElementById('upload-status');
        const statusIcon = document.getElementById('status-icon');
        const statusMessage = document.getElementById('status-message');
        const progressContainer = document.getElementById('progress-container');
        const progressBar = document.getElementById('progress-bar');

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropArea.classList.add('border-primary', 'bg-blue-50');
        }

        function unhighlight() {
            dropArea.classList.remove('border-primary', 'bg-blue-50');
        }

        dropArea.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }

        fileInput.addEventListener('change', function() {
            handleFiles(this.files);
        });

        function handleFiles(files) {
            if (files.length === 0) return;

            const file = files[0];
            if (!allowedFile(file.name, ['pdf', 'txt'])) {
                showStatus('error', '仅支持 PDF 和 TXT 文件');
                return;
            }

            showStatus('processing', '正在上传文件...');

            // 模拟上传进度
            let progress = 0;
            const interval = setInterval(() => {
                progress += 5;
                progressBar.style.width = `${progress}%`;
                if (progress >= 100) {
                    clearInterval(interval);
                    uploadFile(file);
                }
            }, 100);
        }

        function allowedFile(filename, allowedExtensions) {
            const extension = filename.split('.').pop().toLowerCase();
            return allowedExtensions.includes(extension);
        }

        function showStatus(type, message) {
            uploadStatus.classList.remove('hidden');
            progressContainer.classList.remove('hidden');

            if (type === 'success') {
                statusIcon.className = 'fa fa-check-circle text-green-500 mr-2';
            } else if (type === 'error') {
                statusIcon.className = 'fa fa-times-circle text-red-500 mr-2';
                progressContainer.classList.add('hidden');
            } else {
                statusIcon.className = 'fa fa-spinner fa-spin text-primary mr-2';
            }

            statusMessage.textContent = message;
        }

        function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showStatus('success', '文件上传并处理成功！');
                } else {
                    showStatus('error', `上传失败: ${data.message}`);
                }
            })
            .catch(error => {
                showStatus('error', '上传过程中出错');
                console.error('上传错误:', error);
            });
        }

        // 查询处理
        const queryInput = document.getElementById('query-input');
        const searchButton = document.getElementById('search-button');
        const answerContainer = document.getElementById('answer-container');
        const answerContent = document.getElementById('answer-content');
        const modelName = document.getElementById('model-name');
        const referenceContainer = document.getElementById('reference-container');
        const referenceList = document.getElementById('reference-list');
        const queryStatus = document.getElementById('query-status');

        searchButton.addEventListener('click', handleQuery);
        queryInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleQuery();
            }
        });

        function handleQuery() {
            const query = queryInput.value.trim();
            if (!query) return;

            // 显示查询状态
            queryStatus.classList.remove('hidden');
            answerContainer.classList.add('hidden');
            referenceContainer.classList.add('hidden');

            fetch('/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ query: query })
            })
            .then(response => response.json())
            .then(data => {
                queryStatus.classList.add('hidden');

                if (data.status === 'success') {
                    answerContent.innerHTML = data.answer;
                    modelName.textContent = data.model || '未指定';
                    answerContainer.classList.remove('hidden');

                    // 显示参考文档（带准确率）
                    if (data.references && data.references.length > 0) {
                        referenceList.innerHTML = '';
                        data.references.forEach(ref => {
                            const accuracy = ref.accuracy !== undefined ? ref.accuracy : 0;
                            const referenceElement = document.createElement('div');
                            referenceElement.className = 'bg-card rounded-lg border border-gray-100 overflow-hidden';

                            // 添加准确率进度条
                            referenceElement.innerHTML = `
                                <div class="flex justify-between items-start p-4">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center mb-2">
                                            <span class="text-xs font-medium px-2 py-1 rounded-full bg-accuracy/20 text-accuracy">准确率: ${accuracy}%</span>
                                        </div>
                                        <div class="accuracy-bar mb-2">
                                            <div class="accuracy-fill" style="width: ${accuracy}%"></div>
                                        </div>
                                        <p class="text-sm text-gray-700 whitespace-pre-wrap break-words">${ref.text}</p>
                                    </div>
                                </div>
                            `;
                            referenceList.appendChild(referenceElement);
                        });
                        referenceContainer.classList.remove('hidden');
                    }
                } else {
                    answerContent.innerHTML = `<p class="text-red-500">${data.message}</p>`;
                    answerContainer.classList.remove('hidden');
                }
            })
            .catch(error => {
                queryStatus.classList.add('hidden');
                answerContent.innerHTML = `<p class="text-red-500">查询失败: ${error.message}</p>`;
                answerContainer.classList.remove('hidden');
                console.error('查询错误:', error);
            });
        }
    </script>
</body>
</html>