from pymilvus import connections, FieldSchema, CollectionSchema, DataType, Collection, utility
import logging
import time
from typing import Dict, List, Optional, Tuple
import numpy as np

# 全局Milvus集合
collection = None

# 索引配置常量
INDEX_CONFIG = {
    "HNSW": {
        "index_type": "HNSW",
        "metric_type": "L2",
        "params": {
            "M": 16,  # 每个节点的最大连接数，影响构建时间和查询精度
            "efConstruction": 200,  # 构建时的候选数量，影响构建质量
        }
    },
    "IVF_FLAT": {
        "index_type": "IVF_FLAT",
        "metric_type": "L2",
        "params": {"nlist": 1024}  # 聚类中心数量
    },
    "IVF_SQ8": {
        "index_type": "IVF_SQ8",
        "metric_type": "L2",
        "params": {"nlist": 2048}
    }
}


def init_milvus(index_type: str = "HNSW", force_rebuild: bool = False):
    """
    初始化Milvus连接和集合，使用优化的索引配置

    Args:
        index_type: 索引类型 ("HNSW", "IVF_FLAT", "IVF_SQ8")
        force_rebuild: 是否强制重建索引

    Returns:
        Collection: Milvus集合对象
    """
    global collection
    try:
        start_time = time.time()

        # 连接到Milvus
        connections.connect("default", host="localhost", port="19530")
        logging.info("成功连接到Milvus服务器")

        # 定义集合名称
        collection_name = "document_vectors"

        # 检查集合是否已存在
        collection_exists = utility.has_collection(collection_name)

        if not collection_exists:
            # 创建新集合
            collection = _create_optimized_collection(collection_name, index_type)
        else:
            # 加载现有集合
            collection = Collection(collection_name)

            # 检查是否需要重建索引
            if force_rebuild or _should_rebuild_index(collection, index_type):
                logging.info("检测到索引需要优化，开始重建...")
                collection = _rebuild_collection_index(collection, index_type)

        # 优化集合加载配置
        _load_collection_optimized(collection)

        init_time = time.time() - start_time
        logging.info(f"Milvus初始化完成，耗时: {init_time:.2f}秒")

        return collection

    except Exception as e:
        logging.error(f"Milvus初始化失败: {str(e)}")
        raise


def _create_optimized_collection(collection_name: str, index_type: str) -> Collection:
    """
    创建优化的集合和索引

    Args:
        collection_name: 集合名称
        index_type: 索引类型

    Returns:
        Collection: 创建的集合对象
    """
    try:
        # 定义字段（优化内存使用）
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=32768),  # 减少最大长度以节省内存
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=1024)  # BGE-M3输出维度
        ]

        # 创建集合schema
        schema = CollectionSchema(
            fields,
            "Optimized document vectors for high-performance similarity search",
            enable_dynamic_field=False  # 禁用动态字段以提高性能
        )

        # 创建集合
        collection = Collection(collection_name, schema)
        logging.info(f"创建集合: {collection_name}")

        # 创建优化索引
        index_config = INDEX_CONFIG.get(index_type, INDEX_CONFIG["HNSW"])

        start_time = time.time()
        collection.create_index("vector", index_config)
        index_time = time.time() - start_time

        logging.info(f"创建{index_type}索引完成，耗时: {index_time:.2f}秒")

        return collection

    except Exception as e:
        logging.error(f"创建集合失败: {str(e)}")
        raise


def _should_rebuild_index(collection: Collection, target_index_type: str) -> bool:
    """
    检查是否需要重建索引

    Args:
        collection: 集合对象
        target_index_type: 目标索引类型

    Returns:
        bool: 是否需要重建
    """
    try:
        # 获取当前索引信息
        indexes = collection.indexes
        if not indexes:
            return True

        current_index = indexes[0]
        current_type = current_index.params.get("index_type", "")

        # 如果索引类型不匹配，需要重建
        if current_type != target_index_type:
            logging.info(f"索引类型不匹配: 当前={current_type}, 目标={target_index_type}")
            return True

        return False

    except Exception as e:
        logging.warning(f"检查索引状态失败: {str(e)}")
        return True


def _rebuild_collection_index(collection: Collection, index_type: str) -> Collection:
    """
    重建集合索引

    Args:
        collection: 集合对象
        index_type: 新索引类型

    Returns:
        Collection: 重建后的集合对象
    """
    try:
        # 释放集合
        collection.release()

        # 删除旧索引
        try:
            collection.drop_index()
            logging.info("删除旧索引")
        except:
            pass  # 忽略删除索引的错误

        # 创建新索引
        index_config = INDEX_CONFIG.get(index_type, INDEX_CONFIG["HNSW"])

        start_time = time.time()
        collection.create_index("vector", index_config)
        index_time = time.time() - start_time

        logging.info(f"重建{index_type}索引完成，耗时: {index_time:.2f}秒")

        return collection

    except Exception as e:
        logging.error(f"重建索引失败: {str(e)}")
        raise


def _load_collection_optimized(collection: Collection):
    """
    优化的集合加载配置

    Args:
        collection: 集合对象
    """
    try:
        # 检查集合是否已加载（使用正确的方法）
        try:
            # 尝试获取集合统计信息来检查是否已加载
            collection.get_stats()
            logging.info("集合已加载")
            return
        except Exception:
            # 如果获取统计信息失败，说明集合未加载
            pass

        # 优化加载参数
        load_params = {
            "replica_number": 1,  # 副本数量，减少内存占用
        }

        start_time = time.time()
        collection.load(**load_params)
        load_time = time.time() - start_time

        logging.info(f"集合加载完成，耗时: {load_time:.2f}秒")

    except Exception as e:
        logging.error(f"集合加载失败: {str(e)}")
        raise


def store_vectors(texts: List[str], embeddings: np.ndarray, collection: Collection,
                 batch_size: int = 1000) -> Dict:
    """
    优化的向量存储方法，支持批量处理和内存优化

    Args:
        texts: 文本列表
        embeddings: 嵌入向量数组
        collection: Milvus集合对象
        batch_size: 批处理大小

    Returns:
        Dict: 存储结果
    """
    try:
        start_time = time.time()
        total_count = len(texts)

        if total_count == 0:
            return {"status": "success", "count": 0}

        # 验证数据一致性
        if len(texts) != len(embeddings):
            raise ValueError(f"文本数量({len(texts)})与向量数量({len(embeddings)})不匹配")

        # 批量插入以优化内存使用
        inserted_count = 0

        for i in range(0, total_count, batch_size):
            batch_end = min(i + batch_size, total_count)

            # 准备批次数据
            batch_texts = texts[i:batch_end]
            batch_embeddings = embeddings[i:batch_end]

            # 确保向量格式正确
            if isinstance(batch_embeddings, np.ndarray):
                batch_embeddings = batch_embeddings.tolist()

            batch_data = [
                batch_texts,  # text字段
                batch_embeddings  # vector字段
            ]

            # 插入批次数据
            batch_start_time = time.time()
            mr = collection.insert(batch_data)
            batch_time = time.time() - batch_start_time

            inserted_count += len(batch_texts)

            logging.info(f"批次 {i//batch_size + 1}: 插入 {len(batch_texts)} 个向量，"
                        f"耗时: {batch_time:.2f}秒")

        # 优化刷新策略
        flush_start_time = time.time()
        collection.flush()
        flush_time = time.time() - flush_start_time

        total_time = time.time() - start_time

        logging.info(f"向量存储完成: {inserted_count} 个向量，"
                    f"总耗时: {total_time:.2f}秒，刷新耗时: {flush_time:.2f}秒")

        return {
            "status": "success",
            "count": inserted_count,
            "total_time": total_time,
            "flush_time": flush_time
        }

    except Exception as e:
        logging.error(f"存储向量错误: {str(e)}")
        return {"status": "error", "message": str(e)}


def get_collection_stats(collection: Collection) -> Dict:
    """
    获取集合统计信息

    Args:
        collection: Milvus集合对象

    Returns:
        Dict: 统计信息
    """
    try:
        stats = collection.get_stats()

        # 解析统计信息
        row_count = 0
        for stat in stats:
            if stat.key == "row_count":
                row_count = int(stat.value)
                break

        # 获取索引信息
        indexes = collection.indexes
        index_info = {}
        if indexes:
            index = indexes[0]
            index_info = {
                "type": index.params.get("index_type", "Unknown"),
                "metric": index.params.get("metric_type", "Unknown"),
                "params": {k: v for k, v in index.params.items()
                          if k not in ["index_type", "metric_type"]}
            }

        # 检查集合是否已加载
        is_loaded = False
        try:
            collection.get_stats()
            is_loaded = True
        except Exception:
            is_loaded = False

        return {
            "row_count": row_count,
            "index_info": index_info,
            "is_loaded": is_loaded,
            "collection_name": collection.name
        }

    except Exception as e:
        logging.error(f"获取集合统计信息失败: {str(e)}")
        return {"error": str(e)}


def optimize_collection_memory(collection: Collection) -> bool:
    """
    优化集合内存使用

    Args:
        collection: Milvus集合对象

    Returns:
        bool: 优化是否成功
    """
    try:
        # 压缩集合数据
        collection.compact()
        logging.info("集合数据压缩完成")

        # 等待压缩完成
        import time
        time.sleep(1)

        return True

    except Exception as e:
        logging.error(f"集合内存优化失败: {str(e)}")
        return False