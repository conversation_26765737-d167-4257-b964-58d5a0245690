import logging
import numpy as np
import time
from typing import List, Dict, Optional, Tuple, Any
from pymilvus import Collection
import hashlib
from functools import lru_cache


# 搜索结果缓存
_search_cache = {}
_cache_max_size = 1000

# 动态搜索参数配置
SEARCH_PARAMS_CONFIG = {
    "HNSW": {
        "metric_type": "L2",
        "params": {"ef": 64}  # HNSW搜索时的候选数量
    },
    "IVF_FLAT": {
        "metric_type": "L2",
        "params": {"nprobe": 16}  # IVF搜索的聚类中心数量
    },
    "IVF_SQ8": {
        "metric_type": "L2",
        "params": {"nprobe": 16}
    }
}


def search_similar(query: str, collection: Collection, embedding_generator,
                  top_k: int = 10, use_cache: bool = True) -> List[Dict]:
    """
    优化的向量相似性搜索，支持缓存和动态参数调整

    Args:
        query: 查询文本
        collection: Milvus集合对象
        embedding_generator: 嵌入生成函数
        top_k: 返回的结果数量
        use_cache: 是否使用缓存

    Returns:
        相似文本列表，每个元素包含文本内容、距离和ID
    """
    try:
        start_time = time.time()

        # 检查缓存
        if use_cache:
            cache_key = _generate_cache_key(query, top_k)
            if cache_key in _search_cache:
                logging.info(f"使用缓存结果，查询: {query[:50]}...")
                return _search_cache[cache_key]

        # 生成查询向量（优化内存使用）
        query_embedding = _generate_query_embedding(query, embedding_generator)
        if query_embedding is None:
            return []

        # 验证向量维度
        if not _validate_vector_dimension(query_embedding, collection):
            return []

        # 获取优化的搜索参数
        search_params = _get_optimized_search_params(collection, top_k)

        # 执行优化搜索
        results = _execute_optimized_search(
            query_embedding, collection, search_params, top_k
        )

        # 处理和格式化结果
        similar_texts = _process_search_results(results)

        # 缓存结果
        if use_cache and similar_texts:
            _cache_search_result(cache_key, similar_texts)

        search_time = time.time() - start_time
        logging.info(f"向量搜索完成: {len(similar_texts)} 个结果，耗时: {search_time:.3f}秒")

        return similar_texts

    except Exception as e:
        logging.error(f"向量搜索失败: {str(e)}", exc_info=True)
        return []


def _generate_cache_key(query: str, top_k: int) -> str:
    """生成缓存键"""
    content = f"{query}_{top_k}"
    return hashlib.md5(content.encode()).hexdigest()[:16]


def _generate_query_embedding(query: str, embedding_generator) -> Optional[np.ndarray]:
    """
    生成查询向量，优化内存使用

    Args:
        query: 查询文本
        embedding_generator: 嵌入生成函数

    Returns:
        Optional[np.ndarray]: 查询向量或None
    """
    try:
        # 生成嵌入向量
        query_embedding = embedding_generator([query])

        # 确保是NumPy数组格式
        if isinstance(query_embedding, list):
            query_embedding = np.array(query_embedding, dtype=np.float32)
        elif not isinstance(query_embedding, np.ndarray):
            query_embedding = np.array(query_embedding, dtype=np.float32)

        # 确保数据类型为float32以节省内存
        if query_embedding.dtype != np.float32:
            query_embedding = query_embedding.astype(np.float32)

        return query_embedding

    except Exception as e:
        logging.error(f"生成查询向量失败: {str(e)}")
        return None


def _validate_vector_dimension(query_embedding: np.ndarray, collection: Collection) -> bool:
    """
    验证向量维度是否匹配

    Args:
        query_embedding: 查询向量
        collection: Milvus集合

    Returns:
        bool: 维度是否匹配
    """
    try:
        # 获取集合向量维度
        dim = None
        for field in collection.schema.fields:
            if field.name == "vector":
                dim = field.dim
                break

        if dim is None:
            logging.error("在集合中找不到向量字段")
            return False

        # 检查维度匹配
        if len(query_embedding.shape) == 1:
            query_dim = query_embedding.shape[0]
        else:
            query_dim = query_embedding.shape[1]

        if query_dim != dim:
            logging.error(f"向量维度不匹配! 查询向量维度: {query_dim}, 集合维度: {dim}")
            return False

        return True

    except Exception as e:
        logging.error(f"验证向量维度失败: {str(e)}")
        return False


def _get_optimized_search_params(collection: Collection, top_k: int) -> Dict:
    """
    获取优化的搜索参数

    Args:
        collection: Milvus集合
        top_k: 返回结果数量

    Returns:
        Dict: 搜索参数
    """
    try:
        # 获取索引类型
        indexes = collection.indexes
        if not indexes:
            # 默认参数
            return {
                "metric_type": "L2",
                "params": {"nprobe": min(16, max(1, top_k))}
            }

        index_type = indexes[0].params.get("index_type", "IVF_SQ8")

        # 根据索引类型和top_k动态调整参数
        if index_type == "HNSW":
            # HNSW参数优化：ef值影响搜索质量和速度
            ef_value = max(top_k, min(200, top_k * 4))
            return {
                "metric_type": "L2",
                "params": {"ef": ef_value}
            }
        elif index_type in ["IVF_FLAT", "IVF_SQ8"]:
            # IVF参数优化：nprobe值影响搜索质量和速度
            nprobe_value = max(1, min(64, top_k * 2))
            return {
                "metric_type": "L2",
                "params": {"nprobe": nprobe_value}
            }
        else:
            # 默认参数
            return {
                "metric_type": "L2",
                "params": {"nprobe": 16}
            }

    except Exception as e:
        logging.warning(f"获取搜索参数失败，使用默认参数: {str(e)}")
        return {
            "metric_type": "L2",
            "params": {"nprobe": 16}
        }


def _execute_optimized_search(query_embedding: np.ndarray, collection: Collection,
                             search_params: Dict, top_k: int) -> Any:
    """
    执行优化的向量搜索

    Args:
        query_embedding: 查询向量
        collection: Milvus集合
        search_params: 搜索参数
        top_k: 返回结果数量

    Returns:
        搜索结果
    """
    try:
        # 准备查询数据
        if len(query_embedding.shape) == 1:
            query_data = [query_embedding.tolist()]
        else:
            query_data = [query_embedding.tolist()[0]]

        # 执行搜索
        search_start_time = time.time()

        results = collection.search(
            data=query_data,
            anns_field="vector",
            param=search_params,
            limit=top_k,
            output_fields=["text"],
            consistency_level="Strong"  # 确保数据一致性
        )

        search_time = time.time() - search_start_time
        logging.debug(f"Milvus搜索耗时: {search_time:.3f}秒")

        return results

    except Exception as e:
        logging.error(f"执行向量搜索失败: {str(e)}")
        raise


def _process_search_results(results: Any) -> List[Dict]:
    """
    处理和格式化搜索结果

    Args:
        results: Milvus搜索结果

    Returns:
        List[Dict]: 格式化的结果列表
    """
    similar_texts = []

    try:
        for hits in results:
            for hit in hits:
                # 获取文本内容
                text_content = hit.entity.get("text", "")

                # 过滤空文本
                if not text_content or not text_content.strip():
                    continue

                # 计算相似度分数（距离转换为相似度）
                distance = float(hit.distance)
                similarity_score = max(0.0, 1.0 / (1.0 + distance))

                result_item = {
                    "text": text_content.strip(),
                    "distance": distance,
                    "similarity": similarity_score,
                    "id": int(hit.id)
                }

                similar_texts.append(result_item)

        # 按相似度排序（降序）
        similar_texts.sort(key=lambda x: x["similarity"], reverse=True)

        return similar_texts

    except Exception as e:
        logging.error(f"处理搜索结果失败: {str(e)}")
        return []


def _cache_search_result(cache_key: str, results: List[Dict]):
    """
    缓存搜索结果

    Args:
        cache_key: 缓存键
        results: 搜索结果
    """
    global _search_cache

    try:
        # 检查缓存大小限制
        if len(_search_cache) >= _cache_max_size:
            # 删除最旧的缓存项（简单的LRU策略）
            oldest_key = next(iter(_search_cache))
            del _search_cache[oldest_key]

        # 添加到缓存
        _search_cache[cache_key] = results

    except Exception as e:
        logging.warning(f"缓存搜索结果失败: {str(e)}")


def search_similar_batch(queries: List[str], collection: Collection,
                        embedding_generator, top_k: int = 10) -> List[List[Dict]]:
    """
    批量向量搜索，提高处理效率

    Args:
        queries: 查询文本列表
        collection: Milvus集合对象
        embedding_generator: 嵌入生成函数
        top_k: 每个查询返回的结果数量

    Returns:
        List[List[Dict]]: 每个查询的搜索结果列表
    """
    try:
        start_time = time.time()

        if not queries:
            return []

        # 批量生成嵌入向量
        batch_embeddings = embedding_generator(queries)

        if isinstance(batch_embeddings, list):
            batch_embeddings = np.array(batch_embeddings, dtype=np.float32)

        # 验证维度
        if not _validate_vector_dimension(batch_embeddings[0:1], collection):
            return [[] for _ in queries]

        # 获取搜索参数
        search_params = _get_optimized_search_params(collection, top_k)

        # 执行批量搜索
        results = collection.search(
            data=batch_embeddings.tolist(),
            anns_field="vector",
            param=search_params,
            limit=top_k,
            output_fields=["text"],
            consistency_level="Strong"
        )

        # 处理每个查询的结果
        all_results = []
        for i, hits in enumerate(results):
            query_results = []
            for hit in hits:
                text_content = hit.entity.get("text", "")
                if text_content and text_content.strip():
                    distance = float(hit.distance)
                    similarity_score = max(0.0, 1.0 / (1.0 + distance))

                    query_results.append({
                        "text": text_content.strip(),
                        "distance": distance,
                        "similarity": similarity_score,
                        "id": int(hit.id)
                    })

            # 按相似度排序
            query_results.sort(key=lambda x: x["similarity"], reverse=True)
            all_results.append(query_results)

        batch_time = time.time() - start_time
        logging.info(f"批量搜索完成: {len(queries)} 个查询，耗时: {batch_time:.3f}秒")

        return all_results

    except Exception as e:
        logging.error(f"批量向量搜索失败: {str(e)}")
        return [[] for _ in queries]


def clear_search_cache():
    """清空搜索缓存"""
    global _search_cache
    _search_cache.clear()
    logging.info("搜索缓存已清空")


def get_cache_stats() -> Dict:
    """
    获取缓存统计信息

    Returns:
        Dict: 缓存统计信息
    """
    return {
        "cache_size": len(_search_cache),
        "max_cache_size": _cache_max_size,
        "cache_usage": len(_search_cache) / _cache_max_size * 100
    }