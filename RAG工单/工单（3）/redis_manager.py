# -*- coding: utf-8 -*-
import redis
import json
import logging

# Redis 连接配置
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_DB = 0
SESSION_EXPIRE = 1800  # 30分钟过期时间

class RedisManager:
    def __init__(self):
        try:
            self.connection = redis.Redis(
                host=REDIS_HOST,
                port=REDIS_PORT,
                db=REDIS_DB,
                decode_responses=True
            )
            self.connection.ping()
            logging.info("Redis 连接成功")
        except Exception as e:
            logging.error(f"Redis 连接失败: {str(e)}")
            raise

    def store_session(self, session_id, history):
        try:
            self.connection.setex(
                f"session:{session_id}",
                SESSION_EXPIRE,
                json.dumps(history)
            )
            logging.info(f"会话已保存: {session_id}")
        except Exception as e:
            logging.error(f"保存会话失败: {str(e)}")

    def get_session(self, session_id):
        try:
            session_data = self.connection.get(f"session:{session_id}")
            return json.loads(session_data) if session_data else []
        except Exception as e:
            logging.error(f"获取会话失败: {str(e)}")
            return []

# 全局 Redis 管理器
redis_manager = RedisManager()