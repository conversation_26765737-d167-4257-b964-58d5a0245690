<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能文档问答系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        tertiary: '#8B5CF6',
                        neutral: '#64748B',
                        dark: '#1E293B',
                        light: '#F8FAFC',
                        card: '#F9FAFB',
                        accuracy: '#F59E0B' // 新增准确率颜色
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                    boxShadow: {
                        card: '0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03)',
                        hover: '0 10px 15px -3px rgba(0, 0, 0, 0.07)'
                    }
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .shadow-soft {
                box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
            }
            .transition-custom {
                transition: all 0.3s ease;
            }
            .prose-custom {
                line-height: 1.7;
                max-width: none;
            }
            .prose-custom h3 {
                font-size: 1.25rem;
                font-weight: 600;
                margin-top: 1.5rem;
                margin-bottom: 1rem;
                color: #1E293B;
            }
            .prose-custom p {
                margin-bottom: 1rem;
            }
            .prose-custom ul {
                list-style-type: disc;
                padding-left: 1.5rem;
                margin-bottom: 1rem;
            }
            .prose-custom ol {
                list-style-type: decimal;
                padding-left: 1.5rem;
                margin-bottom: 1rem;
            }
            .prose-custom li {
                margin-bottom: 0.5rem;
            }
            .prose-custom blockquote {
                border-left: 4px solid #3B82F6;
                padding-left: 1rem;
                margin: 1.5rem 0;
                color: #64748B;
                font-style: italic;
            }
            .prose-custom strong {
                font-weight: 600;
            }
            /* 新增准确率进度条样式 */
            .accuracy-bar {
                height: 6px;
                border-radius: 3px;
                background-color: #E5E7EB;
                overflow: hidden;
            }
            .accuracy-fill {
                height: 100%;
                background-color: #F59E0B;
            }

            /* 对话气泡样式 */
            .chat-message {
                max-width: 80%;
                margin-bottom: 1rem;
                animation: fadeInUp 0.3s ease-out;
            }

            .chat-message.user {
                margin-left: auto;
            }

            .chat-message.assistant {
                margin-right: auto;
            }

            .message-bubble {
                padding: 0.75rem 1rem;
                border-radius: 1rem;
                word-wrap: break-word;
                position: relative;
            }

            .message-bubble.user {
                background: linear-gradient(135deg, #3B82F6, #1D4ED8);
                color: white;
                border-bottom-right-radius: 0.25rem;
            }

            .message-bubble.assistant {
                background: white;
                color: #374151;
                border: 1px solid #E5E7EB;
                border-bottom-left-radius: 0.25rem;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            .message-timestamp {
                font-size: 0.75rem;
                color: #9CA3AF;
                margin-top: 0.25rem;
            }

            .message-avatar {
                width: 2rem;
                height: 2rem;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0.875rem;
                font-weight: 600;
                margin-bottom: 0.5rem;
            }

            .message-avatar.user {
                background: linear-gradient(135deg, #3B82F6, #1D4ED8);
                color: white;
                margin-left: auto;
            }

            .message-avatar.assistant {
                background: linear-gradient(135deg, #10B981, #059669);
                color: white;
            }

            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            /* 滚动条样式 */
            #chat-history::-webkit-scrollbar {
                width: 6px;
            }

            #chat-history::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
            }

            #chat-history::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 3px;
            }

            #chat-history::-webkit-scrollbar-thumb:hover {
                background: #a8a8a8;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen font-sans">
    <header class="bg-gradient-to-r from-primary to-tertiary shadow-lg sticky top-0 z-10">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <div class="bg-white p-2 rounded-full shadow-md">
                    <i class="fa fa-file-text-o text-primary text-xl"></i>
                </div>
                <h1 class="text-xl font-bold text-white">智能文档问答系统</h1>
            </div>
            <div class="text-xs text-white/80 bg-black/10 px-2 py-1 rounded">
                基于BGE-M3嵌入模型、BGE-reranker重排序和Milvus向量数据库
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8 max-w-6xl">
        <!-- 文件上传区域 -->
        <section class="bg-white rounded-xl shadow-card p-6 mb-8 transition-custom hover:shadow-hover">
            <h2 class="text-lg font-semibold mb-4 flex items-center">
                <i class="fa fa-cloud-upload text-primary mr-2"></i>
                上传文档
            </h2>
            <p class="text-neutral text-sm mb-4">支持上传PDF和TXT文件，系统将处理并存储文档内容以便后续查询</p>

            <div id="drop-area" class="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center transition-custom hover:border-primary bg-card">
                <i class="fa fa-file-text-o text-4xl text-neutral mb-3"></i>
                <p class="mb-2 text-neutral-700">拖放文件到此处，或</p>
                <label class="inline-block bg-primary text-white px-4 py-2 rounded-lg cursor-pointer hover:bg-primary/90 transition-custom shadow-md">
                    <i class="fa fa-folder-open mr-1"></i> 选择文件
                    <input type="file" id="file-input" class="hidden" accept=".pdf,.txt">
                </label>
                <p class="text-xs text-neutral/60 mt-3">支持 PDF, TXT 格式</p>
            </div>

            <div id="upload-status" class="mt-4 hidden">
                <div class="flex items-center">
                    <i id="status-icon" class="mr-2"></i>
                    <span id="status-message"></span>
                </div>
                <div id="progress-container" class="mt-2 h-2 bg-gray-200 rounded-full overflow-hidden hidden">
                    <div id="progress-bar" class="h-full bg-secondary w-0 transition-all duration-300"></div>
                </div>
            </div>
        </section>

        <!-- 多轮对话区域 -->
        <section class="bg-white rounded-xl shadow-card mb-8 transition-custom hover:shadow-hover">
            <!-- 对话头部 -->
            <div class="flex justify-between items-center p-6 border-b border-gray-100">
                <h2 class="text-lg font-semibold flex items-center">
                    <i class="fa fa-comments text-secondary mr-2"></i>
                    智能对话
                </h2>
                <div class="flex items-center space-x-3">
                    <div class="text-sm text-neutral" id="session-info">
                        <span id="message-count">0</span> 条对话
                    </div>
                    <button id="clear-chat-btn" class="text-sm bg-red-50 text-red-600 px-3 py-1 rounded-lg hover:bg-red-100 transition-custom">
                        <i class="fa fa-trash mr-1"></i> 清除对话
                    </button>
                </div>
            </div>

            <!-- 对话历史区域 -->
            <div id="chat-history" class="h-96 overflow-y-auto p-6 space-y-4 bg-gray-50">
                <div class="text-center text-neutral-500 text-sm" id="empty-chat-message">
                    <i class="fa fa-comment-o text-2xl mb-2"></i>
                    <p>开始您的第一个问题吧！</p>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="p-6 border-t border-gray-100">
                <div class="flex space-x-3">
                    <input type="text" id="chat-input" placeholder="输入您的问题..."
                           class="flex-1 px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent transition-custom">
                    <button id="send-btn" class="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-custom disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fa fa-paper-plane mr-1"></i> 发送
                    </button>
                </div>

                <!-- 输入状态 -->
                <div id="input-status" class="mt-3 hidden flex items-center text-sm text-neutral">
                    <i class="fa fa-spinner fa-spin text-primary mr-2"></i>
                    <span>AI正在思考中...</span>
                </div>
            </div>
        </section>

        <!-- 参考文档区域 -->
        <section id="reference-section" class="bg-white rounded-xl shadow-card p-6 mb-8 transition-custom hover:shadow-hover hidden">
            <h3 class="text-lg font-semibold mb-4 flex items-center">
                <i class="fa fa-file-text-o text-tertiary mr-2"></i>
                参考文档
            </h3>
            <div class="space-y-3" id="reference-list">
                <!-- 动态添加参考文档 -->
            </div>
        </section>

        <!-- 新增：系统信息卡片 -->
        <section class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div class="bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl p-4 border border-primary/20">
                <div class="flex items-center">
                    <div class="bg-primary/10 p-2 rounded-lg mr-3">
                        <i class="fa fa-microchip text-primary text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-primary">嵌入模型</h3>
                        <p class="text-sm">BGE-M3 · 本地推理</p>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-br from-secondary/10 to-secondary/5 rounded-xl p-4 border border-secondary/20">
                <div class="flex items-center">
                    <div class="bg-secondary/10 p-2 rounded-lg mr-3">
                        <i class="fa fa-database text-secondary text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-secondary">向量数据库</h3>
                        <p class="text-sm">Milvus · 1024维</p>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-br from-tertiary/10 to-tertiary/5 rounded-xl p-4 border border-tertiary/20">
                <div class="flex items-center">
                    <div class="bg-tertiary/10 p-2 rounded-lg mr-3">
                        <i class="fa fa-sort-amount-desc text-tertiary text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-tertiary">重排序模型</h3>
                        <p class="text-sm">BGE-reranker-v2-m3</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="bg-dark text-white py-6">
        <div class="container mx-auto px-4 text-center text-sm">
            <p>智能文档问答系统 &copy; 2025 - 基于本地化AI技术构建</p>
            <p class="mt-2 text-white/70">本系统使用本地AI模型处理文档，确保数据安全</p>
        </div>
    </footer>

    <script>
        // 全局变量
        let sessionId = null;
        let chatHistory = [];

        // DOM元素
        const dropArea = document.getElementById('drop-area');
        const fileInput = document.getElementById('file-input');
        const uploadStatus = document.getElementById('upload-status');
        const statusIcon = document.getElementById('status-icon');
        const statusMessage = document.getElementById('status-message');
        const progressContainer = document.getElementById('progress-container');
        const progressBar = document.getElementById('progress-bar');

        // 对话相关元素
        const chatHistoryDiv = document.getElementById('chat-history');
        const chatInput = document.getElementById('chat-input');
        const sendBtn = document.getElementById('send-btn');
        const clearChatBtn = document.getElementById('clear-chat-btn');
        const inputStatus = document.getElementById('input-status');
        const emptyChatMessage = document.getElementById('empty-chat-message');
        const messageCount = document.getElementById('message-count');
        const referenceSection = document.getElementById('reference-section');
        const referenceList = document.getElementById('reference-list');

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropArea.classList.add('border-primary', 'bg-blue-50');
        }

        function unhighlight() {
            dropArea.classList.remove('border-primary', 'bg-blue-50');
        }

        dropArea.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }

        fileInput.addEventListener('change', function() {
            handleFiles(this.files);
        });

        function handleFiles(files) {
            if (files.length === 0) return;

            const file = files[0];
            if (!allowedFile(file.name, ['pdf', 'txt'])) {
                showStatus('error', '仅支持 PDF 和 TXT 文件');
                return;
            }

            showStatus('processing', '正在上传文件...');

            // 模拟上传进度
            let progress = 0;
            const interval = setInterval(() => {
                progress += 5;
                progressBar.style.width = `${progress}%`;
                if (progress >= 100) {
                    clearInterval(interval);
                    uploadFile(file);
                }
            }, 100);
        }

        function allowedFile(filename, allowedExtensions) {
            const extension = filename.split('.').pop().toLowerCase();
            return allowedExtensions.includes(extension);
        }

        function showStatus(type, message) {
            uploadStatus.classList.remove('hidden');
            progressContainer.classList.remove('hidden');

            if (type === 'success') {
                statusIcon.className = 'fa fa-check-circle text-green-500 mr-2';
            } else if (type === 'error') {
                statusIcon.className = 'fa fa-times-circle text-red-500 mr-2';
                progressContainer.classList.add('hidden');
            } else {
                statusIcon.className = 'fa fa-spinner fa-spin text-primary mr-2';
            }

            statusMessage.textContent = message;
        }

        function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showStatus('success', '文件上传并处理成功！');
                } else {
                    showStatus('error', `上传失败: ${data.message}`);
                }
            })
            .catch(error => {
                showStatus('error', '上传过程中出错');
                console.error('上传错误:', error);
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadChatHistory();
            setupEventListeners();
        });

        function setupEventListeners() {
            // 发送消息事件
            sendBtn.addEventListener('click', sendMessage);
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // 清除对话事件
            clearChatBtn.addEventListener('click', clearChatHistory);
        }

        // 发送消息
        function sendMessage() {
            const message = chatInput.value.trim();
            if (!message || sendBtn.disabled) return;

            // 添加用户消息到界面
            addMessageToChat('user', message);
            chatInput.value = '';

            // 禁用发送按钮和显示状态
            sendBtn.disabled = true;
            inputStatus.classList.remove('hidden');

            // 发送请求
            fetch('/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ query: message })
            })
            .then(response => response.json())
            .then(data => {
                inputStatus.classList.add('hidden');
                sendBtn.disabled = false;

                if (data.status === 'success') {
                    // 添加AI回复到界面
                    addMessageToChat('assistant', data.answer);

                    // 更新会话信息
                    if (data.session_id) {
                        sessionId = data.session_id;
                    }

                    // 更新聊天历史
                    if (data.chat_history) {
                        chatHistory = data.chat_history;
                        updateMessageCount();
                    }

                    // 显示参考文档
                    if (data.context && data.context.length > 0) {
                        showReferences(data.context);
                    }
                } else {
                    addMessageToChat('assistant', `错误: ${data.message}`);
                }
            })
            .catch(error => {
                inputStatus.classList.add('hidden');
                sendBtn.disabled = false;
                addMessageToChat('assistant', `发送失败: ${error.message}`);
                console.error('发送错误:', error);
            });
        }

        // 添加消息到聊天界面
        function addMessageToChat(role, content) {
            // 隐藏空消息提示
            emptyChatMessage.style.display = 'none';

            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message ${role}`;

            const avatarDiv = document.createElement('div');
            avatarDiv.className = `message-avatar ${role}`;
            avatarDiv.textContent = role === 'user' ? '我' : 'AI';

            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = `message-bubble ${role}`;

            if (role === 'assistant') {
                // 对AI回复进行Markdown渲染（简单处理）
                bubbleDiv.innerHTML = formatMarkdown(content);
            } else {
                bubbleDiv.textContent = content;
            }

            const timestampDiv = document.createElement('div');
            timestampDiv.className = 'message-timestamp';
            timestampDiv.textContent = new Date().toLocaleTimeString();

            messageDiv.appendChild(avatarDiv);
            messageDiv.appendChild(bubbleDiv);
            messageDiv.appendChild(timestampDiv);

            chatHistoryDiv.appendChild(messageDiv);

            // 滚动到底部
            chatHistoryDiv.scrollTop = chatHistoryDiv.scrollHeight;
        }

        // 简单的Markdown格式化
        function formatMarkdown(text) {
            return text
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 rounded">$1</code>')
                .replace(/\n/g, '<br>');
        }

        // 加载聊天历史
        function loadChatHistory() {
            fetch('/chat/history')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.chat_history) {
                    chatHistory = data.chat_history;
                    sessionId = data.session_id;

                    // 渲染历史消息
                    chatHistory.forEach(msg => {
                        if (msg.role && msg.content) {
                            addMessageToChat(msg.role, msg.content);
                        }
                    });

                    updateMessageCount();
                }
            })
            .catch(error => {
                console.error('加载聊天历史失败:', error);
            });
        }

        // 清除聊天历史
        function clearChatHistory() {
            if (!confirm('确定要清除所有对话记录吗？')) {
                return;
            }

            fetch('/chat/clear', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // 清空界面
                    chatHistoryDiv.innerHTML = '';
                    emptyChatMessage.style.display = 'block';
                    chatHistoryDiv.appendChild(emptyChatMessage);

                    // 隐藏参考文档
                    referenceSection.classList.add('hidden');

                    // 重置变量
                    chatHistory = [];
                    updateMessageCount();

                    alert('对话记录已清除');
                } else {
                    alert('清除失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('清除对话失败:', error);
                alert('清除失败: ' + error.message);
            });
        }

        // 更新消息计数
        function updateMessageCount() {
            messageCount.textContent = chatHistory.length;
        }

        // 显示参考文档
        function showReferences(contexts) {
            if (!contexts || contexts.length === 0) {
                referenceSection.classList.add('hidden');
                return;
            }

            referenceList.innerHTML = '';
            contexts.forEach((context, index) => {
                const referenceElement = document.createElement('div');
                referenceElement.className = 'bg-card rounded-lg border border-gray-100 p-4';

                referenceElement.innerHTML = `
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-6 h-6 bg-tertiary/20 text-tertiary rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-1">
                            ${index + 1}
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm text-gray-700 whitespace-pre-wrap break-words">${context}</p>
                        </div>
                    </div>
                `;
                referenceList.appendChild(referenceElement);
            });

            referenceSection.classList.remove('hidden');
        }
    </script>
</body>
</html>