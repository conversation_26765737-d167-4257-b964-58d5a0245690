import os
import re
import fitz  # PyMuPDF for PDF processing
import logging
import pandas as pd
from typing import List, Dict, Optional, <PERSON><PERSON>


def allowed_file(filename, allowed_extensions):
    return '.' in filename and \
        filename.rsplit('.', 1)[1].lower() in allowed_extensions


def process_pdf(file_path):
    """
    处理PDF文件，提取文本和图像内容 - 使用fitz内置功能，无需OCR
    """
    try:
        doc = fitz.open(file_path)
        text = ""
        total_images = 0

        for page_num, page in enumerate(doc):
            logging.info(f"处理PDF第{page_num + 1}页")

            # 提取页面文本
            page_text = page.get_text("text")

            # 提取页面中的图像信息
            page_images_text = ""
            try:
                # 获取页面中的所有图像
                image_list = page.get_images(full=True)
                if image_list:
                    logging.info(f"第{page_num + 1}页发现{len(image_list)}个图像")

                    for img_index, img in enumerate(image_list):
                        try:
                            # 获取图像基本信息
                            xref = img[0]
                            width = img[2]
                            height = img[3]

                            # 跳过太小的图像（装饰性图标）
                            if width < 50 or height < 50:
                                continue

                            # 尝试获取图像的更多信息
                            try:
                                base_image = doc.extract_image(xref)
                                image_ext = base_image["ext"]
                                image_size = len(base_image["image"])

                                # 生成图像描述信息
                                img_info = f"图像{img_index + 1}({width}x{height}px, {image_ext.upper()}格式, {image_size//1024}KB)"

                                # 判断图像类型
                                if width > height * 1.5:
                                    img_type = "横向图像"
                                elif height > width * 1.5:
                                    img_type = "纵向图像"
                                else:
                                    img_type = "方形图像"

                                # 判断图像大小类别
                                if image_size > 100000:  # 大于100KB
                                    size_desc = "高质量"
                                elif image_size > 10000:  # 大于10KB
                                    size_desc = "中等质量"
                                else:
                                    size_desc = "低质量"

                                page_images_text += f"\n\n[{img_info}] {img_type}，{size_desc}图像"
                                total_images += 1

                                logging.info(f"检测到图像{img_index + 1}: {width}x{height}, {image_ext}, {image_size}字节")

                            except Exception as extract_error:
                                # 如果无法提取图像详细信息，使用基本信息
                                img_info = f"图像{img_index + 1}({width}x{height}px)"
                                page_images_text += f"\n\n[{img_info}] 检测到图像内容"
                                total_images += 1
                                logging.warning(f"图像{img_index + 1}详细信息提取失败，使用基本信息: {str(extract_error)}")

                        except Exception as img_error:
                            logging.warning(f"处理图像{img_index + 1}失败: {str(img_error)}")
                            continue

                # 如果页面文本很少但有图像，可能是图像密集页面
                if len(page_text) < 50 and len(image_list) > 0:
                    page_images_text += f"\n\n[页面分析] 检测到图像密集页面，包含{len(image_list)}个图像对象"

            except Exception as page_error:
                logging.warning(f"第{page_num + 1}页图像分析失败: {str(page_error)}")

            # 尝试提取页面中的绘图对象（如矢量图形、图表等）
            try:
                drawings = page.get_drawings()
                if drawings:
                    page_images_text += f"\n\n[矢量图形] 检测到{len(drawings)}个绘图对象（可能包含图表、图形等）"
                    logging.info(f"第{page_num + 1}页发现{len(drawings)}个绘图对象")
            except Exception as drawing_error:
                logging.warning(f"第{page_num + 1}页绘图对象分析失败: {str(drawing_error)}")

            # 合并页面文本和图像信息
            full_page_text = page_text
            if page_images_text:
                full_page_text += page_images_text

            text += f"\n\n--- 第{page_num + 1}页 ---\n{full_page_text}"

        # 在关闭文档前记录页数
        total_pages = len(doc)
        doc.close()

        # 清理文本
        text = re.sub(r'\s+', ' ', text).strip()

        if total_images > 0:
            logging.info(f"PDF处理完成，共处理{total_pages}页，检测到{total_images}个图像")

        return text

    except Exception as e:
        raise Exception(f"处理PDF错误: {str(e)}")





def process_txt(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            text = f.read()
        # 清理文本
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    except Exception as e:
        raise Exception(f"处理TXT错误: {str(e)}")


def extract_tables_from_page(page) -> List[str]:
    """
    优化的表格提取方法，保证表格完整性

    Args:
        page: PyMuPDF页面对象

    Returns:
        List[str]: 表格文本列表
    """
    tables = []

    try:
        # 方法1: 使用PyMuPDF的表格检测功能
        tabs = page.find_tables()
        logging.info(f"页面发现 {len(tabs)} 个表格")

        for tab_index, tab in enumerate(tabs):
            try:
                # 提取原始表格数据
                table_data = tab.extract()

                if not table_data or len(table_data) < 1:
                    logging.warning(f"表格{tab_index + 1}数据为空，跳过")
                    continue

                # 数据完整性验证和清理
                cleaned_data = _clean_table_data(table_data)

                if not cleaned_data:
                    logging.warning(f"表格{tab_index + 1}清理后数据为空，跳过")
                    continue

                # 转换为DataFrame进行进一步处理
                df = _create_dataframe_from_table(cleaned_data)

                if df is None or df.empty:
                    logging.warning(f"表格{tab_index + 1}无法转换为DataFrame，跳过")
                    continue

                # 生成结构化文本
                table_text = _format_table_to_text(df, tab_index)

                if table_text:
                    tables.append(table_text)
                    logging.info(f"成功提取表格 {tab_index + 1}，大小: {df.shape}")
                else:
                    logging.warning(f"表格{tab_index + 1}格式化失败")

            except Exception as e:
                logging.warning(f"表格{tab_index + 1}提取失败: {str(e)}")
                # 尝试备用方法提取表格
                try:
                    fallback_text = _extract_table_fallback(tab, tab_index)
                    if fallback_text:
                        tables.append(fallback_text)
                        logging.info(f"表格{tab_index + 1}使用备用方法提取成功")
                except Exception as fallback_error:
                    logging.error(f"表格{tab_index + 1}备用方法也失败: {str(fallback_error)}")
                continue

        # 方法2: 如果PyMuPDF未检测到表格，尝试文本模式检测
        if not tables:
            text_tables = _detect_text_tables(page)
            tables.extend(text_tables)

    except Exception as e:
        logging.error(f"页面表格提取失败: {str(e)}")
        # 最后的备用方法：尝试从页面文本中提取类表格结构
        try:
            text_based_tables = _extract_tables_from_text(page.get_text())
            tables.extend(text_based_tables)
        except Exception as text_error:
            logging.error(f"文本模式表格提取也失败: {str(text_error)}")

    return tables


def _clean_table_data(table_data: List[List]) -> List[List]:
    """
    清理表格数据，确保完整性

    Args:
        table_data: 原始表格数据

    Returns:
        List[List]: 清理后的表格数据
    """
    if not table_data:
        return []

    cleaned_data = []
    max_cols = 0

    # 第一遍：确定最大列数并清理数据
    for row in table_data:
        if row:  # 跳过空行
            cleaned_row = []
            for cell in row:
                # 清理单元格内容
                if cell is None:
                    cleaned_cell = ""
                else:
                    cleaned_cell = str(cell).strip()
                    # 移除多余的空白字符
                    cleaned_cell = re.sub(r'\s+', ' ', cleaned_cell)
                cleaned_row.append(cleaned_cell)

            if cleaned_row and any(cell for cell in cleaned_row):  # 确保行不全为空
                cleaned_data.append(cleaned_row)
                max_cols = max(max_cols, len(cleaned_row))

    # 第二遍：标准化列数，确保所有行都有相同的列数
    standardized_data = []
    for row in cleaned_data:
        # 补齐缺失的列
        while len(row) < max_cols:
            row.append("")
        # 截断多余的列（如果有的话）
        row = row[:max_cols]
        standardized_data.append(row)

    return standardized_data


def _create_dataframe_from_table(table_data: List[List]) -> Optional[pd.DataFrame]:
    """
    从表格数据创建DataFrame

    Args:
        table_data: 清理后的表格数据

    Returns:
        Optional[pd.DataFrame]: DataFrame对象或None
    """
    try:
        if not table_data or len(table_data) < 1:
            return None

        # 如果只有一行，将其作为数据行，生成默认列名
        if len(table_data) == 1:
            columns = [f"列{i+1}" for i in range(len(table_data[0]))]
            df = pd.DataFrame([table_data[0]], columns=columns)
        else:
            # 第一行作为列名，其余作为数据
            headers = table_data[0]
            data_rows = table_data[1:]

            # 确保列名唯一
            unique_headers = []
            for i, header in enumerate(headers):
                if not header or header in unique_headers:
                    header = f"列{i+1}"
                unique_headers.append(header)

            df = pd.DataFrame(data_rows, columns=unique_headers)

        # 进一步清理DataFrame
        df = df.fillna('')  # 填充空值
        df = df.astype(str)  # 转换为字符串类型

        # 移除完全空白的行和列
        df = df.loc[:, (df != '').any(axis=0)]  # 移除空列
        df = df.loc[(df != '').any(axis=1), :]  # 移除空行

        return df if not df.empty else None

    except Exception as e:
        logging.error(f"创建DataFrame失败: {str(e)}")
        return None


def _format_table_to_text(df: pd.DataFrame, table_index: int) -> str:
    """
    将DataFrame格式化为结构化文本

    Args:
        df: DataFrame对象
        table_index: 表格索引

    Returns:
        str: 格式化后的表格文本
    """
    try:
        text_parts = [f"表格{table_index + 1}内容："]

        # 添加表格基本信息
        text_parts.append(f"表格大小：{df.shape[0]}行 × {df.shape[1]}列")

        # 添加列标题
        headers = list(df.columns)
        text_parts.append(f"列标题：{' | '.join(headers)}")

        # 添加数据行（限制显示行数以避免过长）
        max_display_rows = 20  # 最多显示20行
        for i, (idx, row) in enumerate(df.iterrows()):
            if i >= max_display_rows:
                text_parts.append(f"... (共{len(df)}行，仅显示前{max_display_rows}行)")
                break

            row_values = []
            for val in row.values:
                # 限制单元格内容长度
                cell_content = str(val)[:100]  # 最多100字符
                if len(str(val)) > 100:
                    cell_content += "..."
                row_values.append(cell_content)

            row_text = ' | '.join(row_values)
            text_parts.append(f"第{i+1}行：{row_text}")

        return '\n'.join(text_parts)

    except Exception as e:
        logging.error(f"表格文本格式化失败: {str(e)}")
        return f"表格{table_index + 1}：格式化失败"


def _extract_table_fallback(tab, table_index: int) -> Optional[str]:
    """
    备用表格提取方法

    Args:
        tab: PyMuPDF表格对象
        table_index: 表格索引

    Returns:
        Optional[str]: 提取的表格文本或None
    """
    try:
        # 尝试获取表格的边界框信息
        bbox = getattr(tab, 'bbox', None)
        if bbox:
            # 基于位置信息的简单文本提取
            return f"表格{table_index + 1}：检测到表格结构，位置({bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f})"
        else:
            return f"表格{table_index + 1}：检测到表格结构，但无法提取详细内容"
    except Exception as e:
        logging.error(f"备用表格提取失败: {str(e)}")
        return None


def _detect_text_tables(page) -> List[str]:
    """
    从页面文本中检测表格结构

    Args:
        page: PyMuPDF页面对象

    Returns:
        List[str]: 检测到的表格文本列表
    """
    tables = []
    try:
        text = page.get_text()
        if not text:
            return tables

        # 检测制表符分隔的表格
        tab_tables = _extract_tab_separated_tables(text)
        tables.extend(tab_tables)

        # 检测管道符分隔的表格
        pipe_tables = _extract_pipe_separated_tables(text)
        tables.extend(pipe_tables)

        # 检测空格对齐的表格
        space_tables = _extract_space_aligned_tables(text)
        tables.extend(space_tables)

    except Exception as e:
        logging.error(f"文本表格检测失败: {str(e)}")

    return tables


def _extract_tables_from_text(text: str) -> List[str]:
    """
    从纯文本中提取表格结构

    Args:
        text: 页面文本

    Returns:
        List[str]: 提取的表格文本列表
    """
    tables = []

    try:
        # 按行分割文本
        lines = text.split('\n')

        # 查找连续的表格行
        table_blocks = []
        current_block = []

        for line in lines:
            line = line.strip()
            if not line:
                if current_block:
                    table_blocks.append(current_block)
                    current_block = []
                continue

            # 检测是否为表格行（包含多个分隔符）
            if _is_table_row(line):
                current_block.append(line)
            else:
                if current_block:
                    table_blocks.append(current_block)
                    current_block = []

        # 处理最后一个块
        if current_block:
            table_blocks.append(current_block)

        # 转换表格块为文本
        for i, block in enumerate(table_blocks):
            if len(block) >= 2:  # 至少2行才认为是表格
                table_text = f"文本表格{i + 1}：\n" + '\n'.join(block)
                tables.append(table_text)

    except Exception as e:
        logging.error(f"文本表格提取失败: {str(e)}")

    return tables


def _extract_tab_separated_tables(text: str) -> List[str]:
    """提取制表符分隔的表格"""
    tables = []
    lines = text.split('\n')

    for i, line in enumerate(lines):
        if '\t' in line and line.count('\t') >= 2:  # 至少3列
            # 查找连续的制表符行
            table_lines = [line]
            j = i + 1
            while j < len(lines) and '\t' in lines[j]:
                table_lines.append(lines[j])
                j += 1

            if len(table_lines) >= 2:
                table_text = f"制表符表格：\n" + '\n'.join(table_lines)
                tables.append(table_text)

    return tables


def _extract_pipe_separated_tables(text: str) -> List[str]:
    """提取管道符分隔的表格"""
    tables = []
    lines = text.split('\n')

    for i, line in enumerate(lines):
        if '|' in line and line.count('|') >= 2:  # 至少包含2个分隔符
            # 查找连续的管道符行
            table_lines = [line]
            j = i + 1
            while j < len(lines) and '|' in lines[j]:
                table_lines.append(lines[j])
                j += 1

            if len(table_lines) >= 2:
                table_text = f"管道符表格：\n" + '\n'.join(table_lines)
                tables.append(table_text)

    return tables


def _extract_space_aligned_tables(text: str) -> List[str]:
    """提取空格对齐的表格"""
    tables = []
    lines = text.split('\n')

    # 这里可以实现更复杂的空格对齐检测逻辑
    # 暂时返回空列表，避免误检测
    return tables


def _is_table_row(line: str) -> bool:
    """
    判断是否为表格行

    Args:
        line: 文本行

    Returns:
        bool: 是否为表格行
    """
    # 检测常见的表格分隔符
    separators = ['\t', '|', '  ', '，', ',']

    for sep in separators:
        if sep in line and line.count(sep) >= 1:
            return True

    # 检测数字和文字混合的模式（可能是表格数据）
    if re.search(r'\d+.*[a-zA-Z\u4e00-\u9fff].*\d+', line):
        return True

    return False


def chunk_text(text, min_chunk_size=200, max_chunk_size=500, overlap=50):
    """
    按段落切分文本并加入重叠

    参数:
        text: 原始文本
        min_chunk_size: 最小分块大小(字符数)
        max_chunk_size: 最大分块大小(字符数)
        overlap: 重叠区域大小(字符数)

    返回:
        分块后的文本列表
    """
    # 按段落分割（考虑多种换行符）
    paragraphs = [p.strip() for p in re.split(r'\n\s*\n', text) if p.strip()]

    chunks = []
    current_chunk = ""

    for para in paragraphs:
        # 如果当前段落很短，直接添加到当前块
        if len(current_chunk) + len(para) < min_chunk_size:
            if current_chunk:
                current_chunk += "\n\n"
            current_chunk += para
            continue

        # 如果当前段落能直接加入当前块
        if len(current_chunk) + len(para) <= max_chunk_size:
            if current_chunk:
                current_chunk += "\n\n"
            current_chunk += para
        else:
            # 处理长段落
            start_idx = 0
            while start_idx < len(para):
                # 计算可以添加到当前块的文本量
                remaining_space = max_chunk_size - len(current_chunk) - 2  # 减去换行符

                if remaining_space > 0:
                    # 添加部分段落到当前块
                    end_idx = min(start_idx + remaining_space, len(para))
                    if current_chunk:
                        current_chunk += "\n\n"
                    current_chunk += para[start_idx:end_idx]
                    start_idx = end_idx

                # 如果当前块达到最大大小，保存它
                if len(current_chunk) >= min_chunk_size:
                    chunks.append(current_chunk)

                    # 创建重叠的新块
                    if overlap > 0 and chunks:
                        last_chunk = chunks[-1]
                        overlap_start = max(0, len(last_chunk) - overlap)
                        current_chunk = last_chunk[overlap_start:]
                    else:
                        current_chunk = ""

                # 如果剩余段落长度超过最大块大小，直接分割
                if len(para) - start_idx > max_chunk_size:
                    end_idx = min(start_idx + max_chunk_size, len(para))
                    chunks.append(para[start_idx:end_idx])
                    start_idx = end_idx

    # 添加最后一个块
    if current_chunk:
        chunks.append(current_chunk)

    return chunks


def process_uploaded_file(file, upload_folder, collection, embedding_generator):
    # 保存文件
    filename = os.path.join(upload_folder, file.filename)
    file.save(filename)

    # 处理文件
    if filename.lower().endswith('.pdf'):
        text = process_pdf(filename)
    else:  # .txt
        text = process_txt(filename)

    # 分块文本 (使用新算法)
    chunks = chunk_text(text, min_chunk_size=200, max_chunk_size=500, overlap=50)

    # 生成嵌入向量
    embeddings = embedding_generator(chunks)

    # 存储向量
    from milvus_manager import store_vectors
    ids = store_vectors(chunks, embeddings, collection)

    # 删除临时文件
    os.remove(filename)

    return {
        "status": "success",
        "message": f"文件处理成功，已存储 {len(chunks)} 个片段",
        "chunks_count": len(chunks)
    }