from pymilvus import connections, FieldSchema, CollectionSchema, DataType, Collection, utility
import logging

# 全局Milvus集合
collection = None


def init_milvus():
    global collection
    try:
        # 连接到Milvus
        connections.connect("default", host="localhost", port="19530")

        # 定义集合名称
        collection_name = "document_vectors"

        # 检查集合是否已存在，不存在则创建
        if not utility.has_collection(collection_name):
            # 定义字段
            fields = [
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=1024)  # BGE-M3输出维度为1024
            ]

            # 创建集合 schema
            schema = CollectionSchema(fields, "Document vectors for similarity search")

            # 创建集合
            collection = Collection(collection_name, schema)

            # 创建更高效的索引
            index_params = {
                "index_type": "IVF_SQ8",  # 优化索引类型
                "metric_type": "L2",
                "params": {"nlist": 2048}  # 增加聚类中心数量提高精度
            }
            collection.create_index("vector", index_params)
            logging.info("创建优化的IVF_SQ8索引")
        else:
            collection = Collection(collection_name)

        # 加载集合
        collection.load()
        return collection
    except Exception as e:
        logging.error(f"Milvus initialization error: {str(e)}")
        raise


def store_vectors(texts, embeddings, collection):
    try:
        data = [
            texts,  # text字段
            embeddings  # vector字段
        ]

        # 插入数据
        mr = collection.insert(data)
        collection.flush()

        logging.info(f"存储了 {len(texts)} 个向量")
        return {"status": "success", "count": len(texts)}
    except Exception as e:
        logging.error(f"存储向量错误: {str(e)}")
        return {"status": "error", "message": str(e)}