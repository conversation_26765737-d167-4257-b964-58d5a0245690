import os
import uuid
import logging
from flask import Flask, request, jsonify, render_template, session
from flask_cors import CORS
from file_processor import allowed_file, process_uploaded_file
from vector_search import search_similar
from llm_client import generate_answer
from embeddings import generate_embeddings, init_embedding_model
from milvus_manager import init_milvus, store_vectors
from llm_client import init_llm_client
from reranker import rerank_documents
from redis_manager import redis_manager  # 引入Redis管理器

app = Flask(__name__)
CORS(app)
app.secret_key = os.urandom(24)  # 设置会话密钥

# 配置
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'txt', 'pdf'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# 确保上传文件夹存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# 初始化日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 全局组件
collection = None
embedding_model = None
llm_client = None


def initialize_application():
    global collection, embedding_model, llm_client
    try:
        logger.info("Initializing application components...")
        collection = init_milvus()
        embedding_model = init_embedding_model()
        llm_client = init_llm_client()
        logger.info("All components initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize components: {str(e)}", exc_info=True)
        return False


# 在应用启动前初始化组件
initialize_application()


# 路由
@app.route('/')
def index():
    # 为新访客生成会话ID
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())
        logger.info(f"新会话开始: {session['session_id']}")
    return render_template('index.html')


@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({"status": "error", "message": "没有文件部分"})

    file = request.files['file']
    if file.filename == '':
        return jsonify({"status": "error", "message": "没有选择文件"})

    if file and allowed_file(file.filename, ALLOWED_EXTENSIONS):
        try:
            result = process_uploaded_file(
                file,
                app.config['UPLOAD_FOLDER'],
                collection,
                generate_embeddings
            )
            return jsonify(result)
        except Exception as e:
            logger.error(f"文件处理错误: {str(e)}", exc_info=True)
            return jsonify({"status": "error", "message": f"处理文件时出错: {str(e)}"})

    return jsonify({"status": "error", "message": "不支持的文件类型"})


@app.route('/query', methods=['POST'])
def handle_query():
    data = request.json
    if not data or 'query' not in data:
        return jsonify({"status": "error", "message": "缺少查询内容"})

    # 获取会话ID
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())
    session_id = session['session_id']

    # 获取历史对话
    chat_history = redis_manager.get_session(session_id)

    query_text = data['query']

    try:
        # 1. 向量搜索（获取更多结果）
        initial_results = search_similar(query_text, collection, generate_embeddings, top_k=15)

        if not initial_results:
            # 添加到历史对话
            new_history = chat_history + [
                {"role": "user", "content": query_text},
                {"role": "assistant", "content": "没有找到相关信息来回答这个问题。请先上传相关文件。"}
            ]
            redis_manager.store_session(session_id, new_history)

            return jsonify({
                "status": "success",
                "session_id": session_id,
                "answer": "没有找到相关信息来回答这个问题。请先上传相关文件。",
                "context": []
            })

        # 2. 文档重排序
        reranked_results = rerank_documents(query_text, initial_results, top_n=5)

        # 3. 生成答案
        answer = generate_answer(query_text, reranked_results, llm_client, chat_history)

        # 更新历史对话
        new_history = chat_history + [
            {"role": "user", "content": query_text},
            {"role": "assistant", "content": answer}
        ]
        redis_manager.store_session(session_id, new_history)

        return jsonify({
            "status": "success",
            "session_id": session_id,
            "answer": answer,
            "context": [doc["text"] for doc in reranked_results[:3]]
        })

    except Exception as e:
        logger.error(f"查询处理错误: {str(e)}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"处理查询时出错: {str(e)}"
        })


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)