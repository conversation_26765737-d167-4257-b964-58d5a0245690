import openai
import logging

# 全局LLM客户端
llm_client = None


def init_llm_client():
    global llm_client
    try:
        llm_client = openai.OpenAI(
            api_key="ms-6f7de516-7098-44af-969d-b1d28d65ec00",
            base_url="https://api-inference.modelscope.cn/v1/",
            timeout=30
        )
        return llm_client
    except Exception as e:
        logging.error(f"LLM client initialization error: {str(e)}")
        raise


def generate_answer(query, context, client, chat_history=[]):
    try:
        # 构建系统消息
        messages = [
            {
                "role": "system",
                "content": "你是一个AI助手，基于上传的文档内容回答用户问题。保持回答简洁专业，使用Markdown格式组织内容。"
            }
        ]

        # 添加历史对话
        messages.extend(chat_history)

        # 添加上下文
        context_text = "\n\n".join([item["text"] for item in context])
        if len(context_text) > 3000:
            context_text = context_text[:3000] + "...[内容已截断]"

        messages.append({
            "role": "system",
            "content": f"文档上下文信息：\n{context_text}"
        })

        # 添加当前查询
        messages.append({
            "role": "user",
            "content": query
        })

        response = client.chat.completions.create(
            model="Qwen/Qwen2.5-Coder-32B-Instruct",
            messages=messages,
            stream=False,
            timeout=20
        )

        return response.choices[0].message.content.strip()

    except openai.RateLimitError:
        logging.error("API请求达到速率限制")
        return "抱歉，当前请求过于频繁，请稍后再试。"
    except Exception as e:
        logging.error(f"生成答案错误: {str(e)}")
        return "生成答案时出现错误，请稍后再试。"