import openai
import logging

# 全局LLM客户端
llm_client = None

def init_llm_client():
    global llm_client
    try:
        llm_client = openai.OpenAI(
            api_key="6f7de516-7098-44af-969d-b1d28d65ec00",  # 替换为您的ModelScope SDK Token
            base_url="https://api-inference.modelscope.cn/v1/"
        )
        return llm_client
    except Exception as e:
        logging.error(f"LLM client initialization error: {str(e)}")
        raise

def generate_answer(query, context, client):
    try:
        context_text = "\n\n".join([item["text"] for item in context])

        messages = [
            {
                "role": "system",
                "content": "你是一个智能客服，根据提供的上下文信息回答用户的问题。如果上下文信息不足，请如实告知。"
            },
            {
                "role": "user",
                "content": f"上下文信息：\n{context_text}\n\n问题：{query}"
            }
        ]

        response = client.chat.completions.create(
            model="Qwen/Qwen2.5-Coder-32B-Instruct",
            messages=messages,
            stream=False
        )

        return response.choices[0].message.content
    except Exception as e:
        logging.error(f"Error generating answer: {str(e)}")
        return f"生成回答时出错: {str(e)}"